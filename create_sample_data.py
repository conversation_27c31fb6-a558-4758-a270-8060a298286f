#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء البيانات التجريبية لنظام إدارة المختبر
"""

import database
import datetime

def create_sample_data():
    """إنشاء البيانات التجريبية"""
    print("=" * 60)
    print("🏥 إنشاء البيانات التجريبية لنظام إدارة المختبر")
    print("=" * 60)
    
    try:
        # إنشاء قاعدة البيانات
        print("📊 إنشاء قاعدة البيانات...")
        db = database.DatabaseManager()
        print("✅ تم إنشاء قاعدة البيانات")
        
        # إضافة أنواع العينات
        print("\n🧪 إضافة أنواع العينات...")
        sample_types = ['دم', 'بول', 'براز', 'لعاب', 'مسحة', 'بلغم']
        for sample_type in sample_types:
            try:
                db.add_sample_type(sample_type)
                print(f"  ✅ {sample_type}")
            except:
                print(f"  ⚠️ {sample_type} (موجود مسبقاً)")
        
        # إضافة جهات الإرسال
        print("\n🏥 إضافة جهات الإرسال...")
        sender_orgs = [
            'مستشفى بغداد التعليمي',
            'مستشفى البصرة العام',
            'مستشفى الموصل المركزي',
            'مستشفى النجف الأشرف',
            'مستشفى كربلاء المقدسة',
            'مستشفى أربيل التخصصي',
            'عيادة خاصة',
            'مركز صحي'
        ]
        for org in sender_orgs:
            try:
                db.add_sender_org(org)
                print(f"  ✅ {org}")
            except:
                print(f"  ⚠️ {org} (موجود مسبقاً)")
        
        # إضافة التحاليل
        print("\n🔬 إضافة التحاليل...")
        tests = [
            'CBC - تعداد الدم الكامل',
            'ESR - سرعة الترسيب',
            'Blood Sugar - سكر الدم',
            'Urea - اليوريا',
            'Creatinine - الكرياتينين',
            'ALT - إنزيم الكبد ALT',
            'AST - إنزيم الكبد AST',
            'Cholesterol - الكوليسترول',
            'Triglycerides - الدهون الثلاثية',
            'Urine Analysis - تحليل البول الكامل',
            'HbA1c - السكر التراكمي',
            'TSH - هرمون الغدة الدرقية',
            'Vitamin D - فيتامين د',
            'Iron - الحديد',
            'Ferritin - الفيريتين'
        ]
        for test in tests:
            try:
                db.add_test(test)
                print(f"  ✅ {test}")
            except:
                print(f"  ⚠️ {test} (موجود مسبقاً)")
        
        # إضافة الفنيين
        print("\n👨‍⚕️ إضافة الفنيين...")
        technicians = [
            'د. أحمد محمد الطبيب',
            'د. فاطمة علي الاختصاصية',
            'أ. محمد حسن الفني',
            'أ. زينب صالح الفنية',
            'د. عبدالله أحمد',
            'أ. مريم خالد'
        ]
        for tech in technicians:
            try:
                db.add_technician(tech)
                print(f"  ✅ {tech}")
            except:
                print(f"  ⚠️ {tech} (موجود مسبقاً)")
        
        # إضافة مرضى تجريبيين
        print("\n👥 إضافة مرضى تجريبيين...")
        patients = [
            ('123456789', 'أحمد محمد علي', 35, 'M', 'بغداد - الكرادة', '07901234567', '', '', 'دم', 'مستشفى بغداد التعليمي', '2024-01-15', '2024-01-15'),
            ('987654321', 'فاطمة حسن أحمد', 28, 'F', 'البصرة - المعقل', '07807654321', '', '', 'بول', 'مستشفى البصرة العام', '2024-01-16', '2024-01-16'),
            ('456789123', 'محمد عبدالله سالم', 42, 'M', 'الموصل - الجامعة', '07754321098', '', '', 'دم', 'مستشفى الموصل المركزي', '2024-01-17', '2024-01-17'),
            ('789123456', 'زينب علي حسين', 31, 'F', 'النجف - المركز', '07701234567', '', '', 'براز', 'مستشفى النجف الأشرف', '2024-01-18', '2024-01-18'),
            ('321654987', 'عبدالرحمن صالح', 25, 'M', 'كربلاء - الحر', '07809876543', '', '', 'دم', 'مستشفى كربلاء المقدسة', '2024-01-19', '2024-01-19'),
            ('654987321', 'سارة أحمد محمد', 33, 'F', 'أربيل - المركز', '07751234567', '', '', 'دم', 'مستشفى أربيل التخصصي', '2024-01-20', '2024-01-20')
        ]
        
        for i, patient in enumerate(patients, 1):
            try:
                db.add_patient(*patient)
                print(f"  ✅ المريض {i}: {patient[1]}")
            except:
                print(f"  ⚠️ المريض {i}: {patient[1]} (موجود مسبقاً)")
        
        # إضافة نتائج تجريبية
        print("\n📋 إضافة نتائج تجريبية...")
        results = [
            (1, 1, 'طبيعي - WBC: 7000, RBC: 4.5M, Hb: 14g/dl', '2024-01-15', 1),
            (1, 2, '15 mm/hr', '2024-01-15', 1),
            (1, 3, '95 mg/dl', '2024-01-15', 1),
            (2, 10, 'طبيعي - لا توجد خلايا صديدية', '2024-01-16', 2),
            (2, 3, '88 mg/dl', '2024-01-16', 2),
            (3, 1, 'طبيعي - جميع القيم ضمن المعدل', '2024-01-17', 3),
            (3, 3, '110 mg/dl', '2024-01-17', 3),
            (3, 4, '35 mg/dl', '2024-01-17', 3),
            (4, 1, 'طبيعي', '2024-01-18', 4),
            (5, 1, 'طبيعي', '2024-01-19', 1),
            (5, 8, '180 mg/dl', '2024-01-19', 1),
            (6, 1, 'طبيعي', '2024-01-20', 2),
            (6, 11, '6.2%', '2024-01-20', 2)
        ]
        
        for i, result in enumerate(results, 1):
            try:
                db.add_result(*result)
                print(f"  ✅ النتيجة {i}")
            except:
                print(f"  ⚠️ النتيجة {i} (موجودة مسبقاً)")
        
        print("\n" + "=" * 60)
        print("🎉 تم إنشاء جميع البيانات التجريبية بنجاح!")
        print("=" * 60)
        print()
        print("📊 ملخص البيانات المضافة:")
        print(f"   👥 المرضى: {len(patients)}")
        print(f"   🧪 أنواع العينات: {len(sample_types)}")
        print(f"   🏥 جهات الإرسال: {len(sender_orgs)}")
        print(f"   🔬 التحاليل: {len(tests)}")
        print(f"   👨‍⚕️ الفنيين: {len(technicians)}")
        print(f"   📋 النتائج: {len(results)}")
        print()
        print("✅ النظام جاهز للاستخدام!")
        
    except Exception as e:
        print(f"\n❌ خطأ في إنشاء البيانات: {e}")
        import traceback
        print(f"📋 تفاصيل الخطأ:\n{traceback.format_exc()}")

if __name__ == "__main__":
    create_sample_data()
    input("\nاضغط Enter للخروج...")
