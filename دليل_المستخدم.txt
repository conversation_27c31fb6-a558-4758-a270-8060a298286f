========================================
دليل المستخدم - نظام إدارة المختبر الطبي
مختبر الصحة العامة المركزي - ذي قار
========================================

🚀 كيفية تشغيل النظام:

1. الطريقة الأولى (الأسهل):
   - انقر نقراً مزدوجاً على ملف "run_lab.bat"
   - سيتم تثبيت المتطلبات وتشغيل النظام تلقائياً

2. الطريقة الثانية:
   - افتح Command Prompt أو PowerShell
   - انتقل إلى مجلد البرنامج
   - اكتب: python setup_and_run.py

3. الطريقة الثالثة (للمطورين):
   - اكتب: python start_lab.py

========================================
📦 المتطلبات المطلوبة:

✅ نظام التشغيل: Windows 10/11
✅ Python 3.8 أو أحدث
✅ المكتبات التالية (سيتم تثبيتها تلقائياً):
   - openpyxl (لملفات Excel)
   - reportlab (لملفات PDF)
   - Pillow (للصور)
   - pywin32 (لتكامل Windows)
   - python-bidi (للنصوص العربية)
   - arabic-reshaper (لتشكيل النصوص العربية)

========================================
🎨 الواجهة الجديدة:

✨ التصميم العصري:
- ألوان داكنة مريحة للعين
- تبويبات على الجهة اليمنى (مناسب للعربية)
- أيقونات تعبيرية للتبويبات
- تأثيرات تمرير سلسة

📱 التبويبات:
📝 إدخال البيانات - لتسجيل المرضى والعينات
⚡ العمل - لإدارة سير العمل والوجبات
📊 النتائج - لعرض وإدخال النتائج
📋 التقارير والإحصائيات - للتقارير والإحصائيات
⚙️ الإعدادات - لإعدادات النظام

========================================
📝 كيفية استخدام التبويبات:

1. تبويب إدخال البيانات:
   - أدخل بيانات المريض الجديد
   - اختر نوع العينة وجهة الإرسال
   - احفظ البيانات
   - اطبع الستيكر إذا لزم الأمر

2. تبويب العمل:
   - أنشئ وجبة عمل جديدة
   - اختر الفترة الزمنية والفني المسؤول
   - أضف العينات للوجبة
   - أرسل الوجبة للنتائج

3. تبويب النتائج:
   - اختر الوجبة المطلوبة
   - أدخل النتائج لكل عينة
   - احفظ النتائج فردياً أو جماعياً

4. تبويب التقارير:
   - ابحث عن المريض أو العينة
   - أنشئ تقرير فردي أو جماعي
   - اطبع التقرير أو احفظه كـ PDF

5. تبويب الإعدادات:
   - أضف أنواع عينات جديدة
   - أدر جهات الإرسال
   - عدّل إعدادات التقرير
   - أضف فنيين جدد

========================================
🔧 حل المشاكل الشائعة:

❌ مشكلة: "Python غير موجود"
✅ الحل: تثبيت Python من python.org

❌ مشكلة: "Module not found"
✅ الحل: تشغيل python install_requirements.py

❌ مشكلة: "Database error"
✅ الحل: تشغيل python create_sample_data.py

❌ مشكلة: النصوص العربية لا تظهر
✅ الحل: تأكد من تثبيت python-bidi و arabic-reshaper

❌ مشكلة: التطبيق لا يفتح
✅ الحل: تحقق من رسائل الخطأ في Terminal

========================================
📊 البيانات التجريبية:

عند التشغيل الأول، سيتم إنشاء:
✅ 6 مرضى تجريبيين
✅ أنواع العينات الأساسية
✅ جهات الإرسال
✅ التحاليل الشائعة
✅ الفنيين
✅ نتائج تجريبية

يمكنك حذف هذه البيانات لاحقاً وإدخال بياناتك الحقيقية.

========================================
📋 نصائح للاستخدام:

💡 نصائح عامة:
- احفظ نسخة احتياطية من قاعدة البيانات دورياً
- استخدم أرقام هوية صحيحة للمرضى
- تأكد من صحة البيانات قبل الحفظ
- اطبع التقارير بانتظام

💡 نصائح للأداء:
- أغلق التطبيقات الأخرى عند الطباعة
- تأكد من اتصال الطابعة
- استخدم خطوط عربية واضحة
- احفظ التقارير كـ PDF للأرشفة

========================================
📞 الدعم والمساعدة:

إذا واجهت أي مشكلة:
1. راجع هذا الدليل أولاً
2. تحقق من رسائل الخطأ
3. تأكد من تثبيت جميع المتطلبات
4. أعد تشغيل النظام

للمساعدة التقنية:
- وصف المشكلة بالتفصيل
- إرفاق لقطة شاشة للخطأ
- ذكر نظام التشغيل المستخدم

========================================
🎉 نصائح للنجاح:

✨ للحصول على أفضل تجربة:
- استخدم شاشة بدقة عالية
- تأكد من إضاءة مناسبة
- استخدم لوحة مفاتيح عربية
- احتفظ بنسخ احتياطية منتظمة

✨ للعمل الفعال:
- نظم البيانات بانتظام
- استخدم البحث السريع
- اطبع التقارير فور الانتهاء
- راجع النتائج قبل الحفظ

========================================
📈 التحديثات المستقبلية:

🔮 ميزات مخططة:
- دعم قواعد بيانات خارجية
- تكامل مع أنظمة المستشفيات
- تطبيق ويب للوصول عن بُعد
- تطبيق موبايل للفنيين

🔮 تحسينات مستمرة:
- تحسين سرعة الأداء
- إضافة تقارير جديدة
- تحسين واجهة المستخدم
- دعم لغات إضافية

========================================
📄 معلومات النظام:

📊 الإصدار: 2.0
📅 تاريخ الإصدار: 2024
👨‍💻 المطور: Augment Agent
🏥 المخصص لـ: مختبر الصحة العامة المركزي - ذي قار

جميع الحقوق محفوظة © 2024

========================================
🌟 شكر وتقدير

تم تطوير هذا النظام بعناية فائقة لخدمة المختبرات الطبية
نتمنى أن يكون مفيداً في تحسين جودة الخدمات الطبية

مع أطيب التمنيات بالنجاح والتوفيق! 🎉

========================================
