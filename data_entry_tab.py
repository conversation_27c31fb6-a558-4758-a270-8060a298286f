import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import sqlite3
from sticker_printer import StickerPrinter
from excel_importer import ExcelImporter

class DataEntryTab:
    def __init__(self, parent, db, colors, font):
        self.parent = parent
        self.db = db
        self.colors = colors
        self.font = font
        self.sticker_printer = StickerPrinter()
        self.excel_importer = ExcelImporter(db, self.sticker_printer)
        
        self.create_widgets()
        self.current_patient_id = None
        
    def create_widgets(self):
        """إنشاء عناصر واجهة إدخال البيانات"""
        # الإطار الرئيسي
        self.main_frame = tk.Frame(self.parent, bg=self.colors['white'])
        
        # عنوان التبويب
        title_label = tk.Label(
            self.main_frame,
            text="إدخال بيانات المرضى والعينات",
            font=('Arial', 14, 'bold'),
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        title_label.pack(pady=10)
        
        # إطار النموذج
        form_frame = tk.LabelFrame(
            self.main_frame,
            text="بيانات المريض",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        form_frame.pack(fill='x', padx=20, pady=10)
        
        # الحقول
        self.create_form_fields(form_frame)
        
        # أزرار العمليات
        self.create_action_buttons()
        
        # جدول البيانات
        self.create_data_table()
        
        # تحديث البيانات
        self.refresh_data()
        
    def create_form_fields(self, parent):
        """إنشاء حقول النموذج"""
        # إطار للحقول
        fields_frame = tk.Frame(parent, bg=self.colors['white'])
        fields_frame.pack(fill='x', padx=10, pady=10)
        
        # تقسيم الحقول إلى عمودين
        left_frame = tk.Frame(fields_frame, bg=self.colors['white'])
        left_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        right_frame = tk.Frame(fields_frame, bg=self.colors['white'])
        right_frame.pack(side='right', fill='both', expand=True, padx=5)
        
        # متغيرات الحقول
        self.vars = {}
        
        # الحقول الأساسية - العمود الأيسر
        left_fields = [
            ("الاسم*", "name", "entry"),
            ("العمر*", "age", "entry"),
            ("الجنس*", "gender", "combo"),
            ("العنوان*", "address", "text"),
            ("رقم الهاتف*", "phone", "entry")
        ]
        
        for i, (label, key, widget_type) in enumerate(left_fields):
            self.create_field(left_frame, label, key, widget_type, i)
        
        # الحقول الإضافية - العمود الأيمن
        right_fields = [
            ("نوع العينة*", "sample_type", "combo"),
            ("جهة الإرسال*", "sender_org", "combo"),
            ("تاريخ سحب العينة*", "collection_date", "date"),
            ("رقم الجواز", "passport", "entry"),
            ("رقم الوصل", "receipt", "entry")
        ]
        
        for i, (label, key, widget_type) in enumerate(right_fields):
            self.create_field(right_frame, label, key, widget_type, i)
        
        # الحقول التلقائية
        auto_frame = tk.Frame(parent, bg=self.colors['white'])
        auto_frame.pack(fill='x', padx=10, pady=5)
        
        # الرقم الوطني (تلقائي)
        tk.Label(auto_frame, text="الرقم الوطني:", font=self.font, bg=self.colors['white']).pack(side='left')
        self.national_id_var = tk.StringVar()
        national_id_entry = tk.Entry(auto_frame, textvariable=self.national_id_var, state='readonly', width=15)
        national_id_entry.pack(side='left', padx=5)
        
        # تاريخ الاستلام (تلقائي)
        tk.Label(auto_frame, text="تاريخ الاستلام:", font=self.font, bg=self.colors['white']).pack(side='left', padx=(20,0))
        self.received_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d %H:%M"))
        received_date_entry = tk.Entry(auto_frame, textvariable=self.received_date_var, state='readonly', width=20)
        received_date_entry.pack(side='left', padx=5)
        
        # التحاليل المطلوبة
        tests_frame = tk.LabelFrame(parent, text="التحاليل المطلوبة", font=self.font, bg=self.colors['white'])
        tests_frame.pack(fill='x', padx=10, pady=10)
        
        self.create_tests_section(tests_frame)
        
    def create_field(self, parent, label, key, widget_type, row):
        """إنشاء حقل واحد"""
        # تسمية الحقل
        tk.Label(
            parent,
            text=label,
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        ).grid(row=row, column=0, sticky='w', pady=5, padx=5)
        
        # إنشاء المتغير
        self.vars[key] = tk.StringVar()
        
        # إنشاء عنصر الإدخال
        if widget_type == "entry":
            widget = tk.Entry(
                parent,
                textvariable=self.vars[key],
                font=self.font,
                width=25
            )
        elif widget_type == "combo":
            widget = ttk.Combobox(
                parent,
                textvariable=self.vars[key],
                font=self.font,
                width=23,
                state='readonly'
            )
            # تحديد القيم حسب نوع الحقل
            if key == "gender":
                widget['values'] = ('M', 'F')
            elif key == "sample_type":
                widget['values'] = self.get_sample_types()
            elif key == "sender_org":
                widget['values'] = self.get_sender_organizations()
        elif widget_type == "text":
            widget = tk.Text(
                parent,
                font=self.font,
                width=25,
                height=3
            )
        elif widget_type == "date":
            widget = tk.Entry(
                parent,
                textvariable=self.vars[key],
                font=self.font,
                width=25
            )
            # تعيين التاريخ الحالي كقيمة افتراضية
            self.vars[key].set(datetime.now().strftime("%Y-%m-%d"))
        
        widget.grid(row=row, column=1, sticky='w', pady=5, padx=5)
        
        # حفظ مرجع للعنصر
        setattr(self, f"{key}_widget", widget)
        
    def create_tests_section(self, parent):
        """إنشاء قسم التحاليل"""
        # إطار للتحاليل
        tests_frame = tk.Frame(parent, bg=self.colors['white'])
        tests_frame.pack(fill='x', padx=10, pady=5)
        
        # قائمة التحاليل المتاحة
        available_tests = self.get_available_tests()
        self.test_vars = {}
        
        # إنشاء checkboxes للتحاليل
        for i, test in enumerate(available_tests):
            var = tk.BooleanVar()
            self.test_vars[test] = var
            
            cb = tk.Checkbutton(
                tests_frame,
                text=test,
                variable=var,
                font=self.font,
                bg=self.colors['white']
            )
            cb.grid(row=i//3, column=i%3, sticky='w', padx=10, pady=2)
        
    def create_action_buttons(self):
        """إنشاء أزرار العمليات"""
        buttons_frame = tk.Frame(self.main_frame, bg=self.colors['white'])
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        # أزرار العمليات الأساسية
        buttons = [
            ("إضافة", self.add_patient, self.colors['primary']),
            ("تعديل", self.update_patient, self.colors['background']),
            ("حذف", self.delete_patient, '#DC3545'),
            ("مسح الحقول", self.clear_fields, self.colors['dark_gray']),
            ("طباعة ستيكر", self.print_sticker, '#28A745'),
            ("استيراد من Excel", self.import_excel, '#6F42C1')
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=self.font,
                bg=color,
                fg=self.colors['white'],
                relief='raised',
                bd=3,
                width=12,
                height=2
            )
            btn.pack(side='left', padx=5)
        
    def create_data_table(self):
        """إنشاء جدول البيانات"""
        # إطار الجدول
        table_frame = tk.LabelFrame(
            self.main_frame,
            text="البيانات المسجلة",
            font=self.font,
            bg=self.colors['white']
        )
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إنشاء Treeview
        columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة', 'جهة الإرسال', 'تاريخ السحب')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=10)
        
        # تحديد عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # ربط حدث النقر
        self.tree.bind('<ButtonRelease-1>', self.on_tree_select)
        
    def get_sample_types(self):
        """الحصول على أنواع العينات"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT name FROM sample_types ORDER BY name')
        types = [row[0] for row in cursor.fetchall()]
        conn.close()
        return types
        
    def get_sender_organizations(self):
        """الحصول على جهات الإرسال"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT name FROM sender_organizations ORDER BY name')
        orgs = [row[0] for row in cursor.fetchall()]
        conn.close()
        return orgs
        
    def get_available_tests(self):
        """الحصول على التحاليل المتاحة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT name FROM available_tests ORDER BY name')
        tests = [row[0] for row in cursor.fetchall()]
        conn.close()
        return tests

    def validate_required_fields(self):
        """التحقق من الحقول الإجبارية"""
        required_fields = ['name', 'age', 'gender', 'phone', 'sample_type', 'sender_org', 'collection_date']

        for field in required_fields:
            if field == 'address':
                value = self.address_widget.get("1.0", tk.END).strip()
            else:
                value = self.vars[field].get().strip()

            if not value:
                field_names = {
                    'name': 'الاسم',
                    'age': 'العمر',
                    'gender': 'الجنس',
                    'address': 'العنوان',
                    'phone': 'رقم الهاتف',
                    'sample_type': 'نوع العينة',
                    'sender_org': 'جهة الإرسال',
                    'collection_date': 'تاريخ سحب العينة'
                }
                messagebox.showerror("خطأ", f"يرجى إدخال {field_names[field]}")
                return False

        # التحقق من صحة العمر
        try:
            age = int(self.vars['age'].get())
            if age <= 0 or age > 150:
                messagebox.showerror("خطأ", "يرجى إدخال عمر صحيح")
                return False
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال عمر صحيح")
            return False

        # التحقق من وجود تحليل واحد على الأقل
        selected_tests = [test for test, var in self.test_vars.items() if var.get()]
        if not selected_tests:
            messagebox.showerror("خطأ", "يرجى اختيار تحليل واحد على الأقل")
            return False

        return True

    def add_patient(self):
        """إضافة مريض جديد"""
        if not self.validate_required_fields():
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # الحصول على الرقم الوطني التالي
            national_id = self.db.get_next_national_id()

            # إدراج بيانات المريض
            cursor.execute('''
                INSERT INTO patients (
                    national_id, name, age, gender, address, phone,
                    passport_number, receipt_number, sample_type,
                    sender_organization, sample_collection_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                national_id,
                self.vars['name'].get(),
                int(self.vars['age'].get()),
                self.vars['gender'].get(),
                self.address_widget.get("1.0", tk.END).strip(),
                self.vars['phone'].get(),
                self.vars['passport'].get() or None,
                self.vars['receipt'].get() or None,
                self.vars['sample_type'].get(),
                self.vars['sender_org'].get(),
                self.vars['collection_date'].get()
            ))

            patient_id = cursor.lastrowid

            # إضافة التحاليل المطلوبة
            selected_tests = [test for test, var in self.test_vars.items() if var.get()]
            for test in selected_tests:
                cursor.execute(
                    'INSERT INTO patient_tests (patient_id, test_name) VALUES (?, ?)',
                    (patient_id, test)
                )

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة المريض بنجاح")

            # طباعة الستيكر تلقائياً
            self.print_sticker_for_patient(patient_id)

            # تحديث الجدول ومسح الحقول
            self.refresh_data()
            self.clear_fields()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة المريض: {str(e)}")

    def update_patient(self):
        """تعديل بيانات المريض"""
        if not self.current_patient_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض للتعديل")
            return

        if not self.validate_required_fields():
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # تحديث بيانات المريض
            cursor.execute('''
                UPDATE patients SET
                    name=?, age=?, gender=?, address=?, phone=?,
                    passport_number=?, receipt_number=?, sample_type=?,
                    sender_organization=?, sample_collection_date=?,
                    updated_at=CURRENT_TIMESTAMP
                WHERE id=?
            ''', (
                self.vars['name'].get(),
                int(self.vars['age'].get()),
                self.vars['gender'].get(),
                self.address_widget.get("1.0", tk.END).strip(),
                self.vars['phone'].get(),
                self.vars['passport'].get() or None,
                self.vars['receipt'].get() or None,
                self.vars['sample_type'].get(),
                self.vars['sender_org'].get(),
                self.vars['collection_date'].get(),
                self.current_patient_id
            ))

            # حذف التحاليل القديمة وإضافة الجديدة
            cursor.execute('DELETE FROM patient_tests WHERE patient_id=?', (self.current_patient_id,))

            selected_tests = [test for test, var in self.test_vars.items() if var.get()]
            for test in selected_tests:
                cursor.execute(
                    'INSERT INTO patient_tests (patient_id, test_name) VALUES (?, ?)',
                    (self.current_patient_id, test)
                )

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم تعديل بيانات المريض بنجاح")

            # تحديث الجدول ومسح الحقول
            self.refresh_data()
            self.clear_fields()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل المريض: {str(e)}")

    def delete_patient(self):
        """حذف المريض"""
        if not self.current_patient_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المريض؟"):
            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # حذف التحاليل المرتبطة
                cursor.execute('DELETE FROM patient_tests WHERE patient_id=?', (self.current_patient_id,))

                # حذف النتائج المرتبطة
                cursor.execute('DELETE FROM test_results WHERE patient_id=?', (self.current_patient_id,))

                # حذف من وجبات العمل
                cursor.execute('DELETE FROM batch_samples WHERE patient_id=?', (self.current_patient_id,))

                # حذف المريض
                cursor.execute('DELETE FROM patients WHERE id=?', (self.current_patient_id,))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم حذف المريض بنجاح")

                # تحديث الجدول ومسح الحقول
                self.refresh_data()
                self.clear_fields()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف المريض: {str(e)}")

    def clear_fields(self):
        """مسح جميع الحقول"""
        for var in self.vars.values():
            var.set("")

        self.address_widget.delete("1.0", tk.END)

        for var in self.test_vars.values():
            var.set(False)

        self.national_id_var.set("")
        self.received_date_var.set(datetime.now().strftime("%Y-%m-%d %H:%M"))
        self.current_patient_id = None

        # تعيين التاريخ الحالي
        self.vars['collection_date'].set(datetime.now().strftime("%Y-%m-%d"))

    def print_sticker(self):
        """طباعة ستيكر للمريض المحدد"""
        if not self.current_patient_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض لطباعة الستيكر")
            return

        self.print_sticker_for_patient(self.current_patient_id)

    def print_sticker_for_patient(self, patient_id):
        """طباعة ستيكر لمريض محدد"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT name, sample_type, national_id
                FROM patients WHERE id=?
            ''', (patient_id,))

            patient_data = cursor.fetchone()
            conn.close()

            if patient_data:
                self.sticker_printer.print_sticker(
                    patient_name=patient_data[0],
                    sample_type=patient_data[1],
                    national_id=patient_data[2]
                )
                messagebox.showinfo("نجح", "تم إرسال الستيكر للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء طباعة الستيكر: {str(e)}")

    def import_excel(self):
        """استيراد البيانات من ملف Excel"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Excel",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )

        if file_path:
            try:
                imported_count = self.excel_importer.import_from_excel(file_path)
                messagebox.showinfo("نجح", f"تم استيراد {imported_count} مريض بنجاح")
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الاستيراد: {str(e)}")

    def on_tree_select(self, event):
        """معالج حدث اختيار عنصر من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            national_id = item['values'][0]

            # البحث عن المريض وتحميل بياناته
            self.load_patient_data(national_id)

    def load_patient_data(self, national_id):
        """تحميل بيانات المريض للتعديل"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM patients WHERE national_id=?
            ''', (national_id,))

            patient = cursor.fetchone()

            if patient:
                self.current_patient_id = patient[0]

                # تحميل البيانات الأساسية
                self.vars['name'].set(patient[2])
                self.vars['age'].set(patient[3])
                self.vars['gender'].set(patient[4])
                self.vars['phone'].set(patient[6])
                self.vars['passport'].set(patient[7] or "")
                self.vars['receipt'].set(patient[8] or "")
                self.vars['sample_type'].set(patient[9])
                self.vars['sender_org'].set(patient[10])
                self.vars['collection_date'].set(patient[11])

                # تحميل العنوان
                self.address_widget.delete("1.0", tk.END)
                self.address_widget.insert("1.0", patient[5])

                # تحميل الرقم الوطني وتاريخ الاستلام
                self.national_id_var.set(patient[1])
                self.received_date_var.set(patient[12])

                # تحميل التحاليل المطلوبة
                cursor.execute('''
                    SELECT test_name FROM patient_tests WHERE patient_id=?
                ''', (self.current_patient_id,))

                selected_tests = [row[0] for row in cursor.fetchall()]

                # مسح جميع التحاليل أولاً
                for var in self.test_vars.values():
                    var.set(False)

                # تحديد التحاليل المختارة
                for test in selected_tests:
                    if test in self.test_vars:
                        self.test_vars[test].set(True)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل بيانات المريض: {str(e)}")

    def refresh_data(self):
        """تحديث جدول البيانات"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT national_id, name, age, gender, sample_type,
                       sender_organization, sample_collection_date
                FROM patients
                ORDER BY national_id DESC
            ''')

            for row in cursor.fetchall():
                self.tree.insert('', 'end', values=row)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث البيانات: {str(e)}")

        # تحديث الرقم الوطني التالي
        next_id = self.db.get_next_national_id()
        self.national_id_var.set(next_id)

    def show(self):
        """إظهار التبويب"""
        self.main_frame.pack(fill='both', expand=True)

    def hide(self):
        """إخفاء التبويب"""
        self.main_frame.pack_forget()
