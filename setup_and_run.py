#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد وتشغيل نظام إدارة المختبر الطبي
"""

import sys
import os
import subprocess

def check_python():
    """التحقق من إصدار Python"""
    print("🐍 التحقق من Python...")
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    print(f"✅ Python {sys.version.split()[0]} متوفر")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 تثبيت المتطلبات...")
    
    packages = [
        "openpyxl",
        "reportlab", 
        "Pillow",
        "pywin32",
        "python-bidi",
        "arabic-reshaper"
    ]
    
    for package in packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} متوفر")
        except ImportError:
            print(f"📦 تثبيت {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ تم تثبيت {package}")
            except:
                print(f"❌ فشل تثبيت {package}")
                return False
    
    return True

def create_database():
    """إنشاء قاعدة البيانات والبيانات التجريبية"""
    print("\n📊 إعداد قاعدة البيانات...")
    
    try:
        import database
        db = database.DatabaseManager()
        print("✅ تم إنشاء قاعدة البيانات")
        
        # إضافة بيانات أساسية
        sample_types = ['دم', 'بول', 'براز']
        for sample_type in sample_types:
            try:
                db.add_sample_type(sample_type)
            except:
                pass
        
        tests = ['CBC - تعداد الدم الكامل', 'Blood Sugar - سكر الدم', 'Urine Analysis - تحليل البول']
        for test in tests:
            try:
                db.add_test(test)
            except:
                pass
        
        orgs = ['مستشفى عام', 'عيادة خاصة']
        for org in orgs:
            try:
                db.add_sender_org(org)
            except:
                pass
        
        techs = ['د. أحمد محمد', 'أ. فاطمة علي']
        for tech in techs:
            try:
                db.add_technician(tech)
            except:
                pass
        
        print("✅ تم إضافة البيانات الأساسية")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def run_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل التطبيق...")
    
    try:
        from main_window import MainWindow
        app = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        print("\n🎉 نظام إدارة المختبر الطبي جاهز!")
        print("=" * 60)
        
        app.run()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        print(traceback.format_exc())
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏥 نظام إدارة المختبر الطبي")
    print("   مختبر الصحة العامة المركزي - ذي قار")
    print("=" * 60)
    
    # التحقق من Python
    if not check_python():
        input("\nاضغط Enter للخروج...")
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("\n❌ فشل في تثبيت المتطلبات")
        input("\nاضغط Enter للخروج...")
        return
    
    # إعداد قاعدة البيانات
    if not create_database():
        print("\n❌ فشل في إعداد قاعدة البيانات")
        input("\nاضغط Enter للخروج...")
        return
    
    # تشغيل التطبيق
    run_application()

if __name__ == "__main__":
    main()
