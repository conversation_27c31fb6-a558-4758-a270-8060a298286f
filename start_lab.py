#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط لنظام إدارة المختبر الطبي
"""

import sys
import os
import traceback

def main():
    """تشغيل التطبيق بطريقة مبسطة"""
    print("=" * 60)
    print("🏥 نظام إدارة المختبر الطبي - مختبر الصحة العامة المركزي")
    print("=" * 60)
    print()
    
    try:
        print("📦 تحميل الوحدات...")
        
        # استيراد الوحدات الأساسية
        import tkinter as tk
        print("✅ تم تحميل tkinter")
        
        from main_window import MainWindow
        print("✅ تم تحميل main_window")
        
        print()
        print("🚀 بدء تشغيل التطبيق...")
        
        # إنشاء وتشغيل التطبيق
        app = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        print()
        print("🎉 التطبيق جاهز للاستخدام!")
        print("=" * 60)
        
        # تشغيل التطبيق
        app.run()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print()
        print("💡 تأكد من تثبيت المكتبات المطلوبة:")
        print("   pip install openpyxl reportlab Pillow pywin32")
        print("   pip install python-bidi arabic-reshaper")
        print()
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print()
        print("📋 تفاصيل الخطأ:")
        print(traceback.format_exc())
        print()
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
