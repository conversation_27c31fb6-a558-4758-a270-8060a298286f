#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظيفة الاختيار الفردي للنتائج
"""

import tkinter as tk
from tkinter import ttk, messagebox

class TestIndividualResult:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("اختبار الاختيار الفردي للنتائج")
        self.root.geometry("800x600")
        
        self.result_options = ['Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND']
        self.selected_item_info = None
        
        self.create_test_interface()
        
    def create_test_interface(self):
        """إنشاء واجهة الاختبار"""
        
        # عنوان
        title_label = tk.Label(
            self.root,
            text="اختبار وظيفة الاختيار الفردي للنتائج",
            font=('Arial', 14, 'bold'),
            fg='blue'
        )
        title_label.pack(pady=10)
        
        # جدول تجريبي
        columns = ('الرقم الوطني', 'اسم المريض', 'نوع العينة', 'التحليل', 'النتيجة', 'الفني', 'تاريخ النتيجة')
        self.results_tree = ttk.Treeview(self.root, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=100, anchor='center')
        
        # إضافة بيانات تجريبية
        test_data = [
            (1, 'أحمد محمد', 'دم', 'فحص كوفيد-19', '🔴 Positive', 'أحمد علي', '2024-01-15'),
            (2, 'فاطمة حسن', 'بول', 'فحص الملاريا', '🟢 Negative', 'فاطمة أحمد', '2024-01-15'),
            (3, 'علي محمد', 'مسحة', 'فحص السل', '🟡 Retest', 'محمد حسن', '2024-01-15'),
            (4, 'زينب أحمد', 'دم', 'فحص التيفوئيد', '⚪ لم يتم إدخال النتيجة', 'زينب علي', ''),
            (5, 'محمد علي', 'براز', 'فحص الكوليرا', '🔵 Recollection', 'أحمد محمد', '2024-01-15')
        ]
        
        for data in test_data:
            self.results_tree.insert('', 'end', values=data)
        
        self.results_tree.pack(fill='both', expand=True, padx=10, pady=10)
        
        # ربط حدث النقر
        self.results_tree.bind('<ButtonRelease-1>', self.on_result_select)
        
        # إطار الاختيار الفردي
        individual_frame = tk.Frame(self.root)
        individual_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(individual_frame, text="النتيجة المختارة:", font=('Arial', 10)).pack(side='left')
        
        self.individual_result_var = tk.StringVar()
        result_combo = ttk.Combobox(
            individual_frame,
            textvariable=self.individual_result_var,
            values=self.result_options,
            width=15,
            state='readonly'
        )
        result_combo.pack(side='left', padx=5)
        
        apply_btn = tk.Button(
            individual_frame,
            text="تطبيق",
            command=self.apply_individual_result,
            bg='green',
            fg='white',
            width=10
        )
        apply_btn.pack(side='left', padx=5)
        
        # منطقة عرض المعلومات
        self.info_text = tk.Text(self.root, height=8, width=80)
        self.info_text.pack(fill='x', padx=10, pady=10)
        
        # إضافة رسالة ترحيب
        welcome_msg = """مرحباً بك في اختبار وظيفة الاختيار الفردي للنتائج!

التعليمات:
1. انقر على أي صف في الجدول أعلاه
2. ستظهر النتيجة الحالية تلقائياً في القائمة المنسدلة
3. يمكنك تغيير النتيجة واختيار نتيجة جديدة
4. اضغط "تطبيق" لمحاكاة تطبيق النتيجة

ستظهر تفاصيل العملية هنا...
"""
        self.info_text.insert('1.0', welcome_msg)
        
    def on_result_select(self, event):
        """معالج حدث اختيار نتيجة"""
        self.log_message("=== بداية on_result_select ===")
        
        selection = self.results_tree.selection()
        self.log_message(f"العناصر المختارة: {selection}")
        
        if selection:
            item = self.results_tree.item(selection[0])
            values = item['values']
            self.log_message(f"قيم العنصر: {values}")
            
            if len(values) >= 5:
                current_result = values[4]
                self.log_message(f"النتيجة الحالية (مع الرموز): '{current_result}'")

                # إزالة الرموز الملونة
                clean_result = current_result
                if current_result:
                    symbols_to_remove = ["🔴 ", "🟢 ", "🟡 ", "🔵 ", "📤 ", "🟣 ", "⚪ "]
                    for symbol in symbols_to_remove:
                        clean_result = clean_result.replace(symbol, "")
                
                self.log_message(f"النتيجة النظيفة: '{clean_result}'")
                self.log_message(f"خيارات النتائج المتاحة: {self.result_options}")

                # تحديد النتيجة في القائمة المنسدلة
                if clean_result and clean_result != "لم يتم إدخال النتيجة":
                    if clean_result in self.result_options:
                        self.log_message(f"✅ تحديد النتيجة في القائمة: {clean_result}")
                        self.individual_result_var.set(clean_result)
                    else:
                        self.log_message(f"❌ النتيجة '{clean_result}' غير موجودة في القائمة")
                        self.individual_result_var.set("")
                else:
                    self.log_message("⚪ لا توجد نتيجة أو النتيجة فارغة")
                    self.individual_result_var.set("")
                
                # حفظ معلومات العنصر المختار
                if len(values) >= 4:
                    self.selected_item_info = {
                        'national_id': values[0],
                        'patient_name': values[1],
                        'test_name': values[3],
                        'current_result': clean_result
                    }
                    
                    self.log_message(f"📋 تم حفظ معلومات العنصر: {self.selected_item_info}")
            else:
                self.log_message("❌ عدد القيم غير كافي")
        else:
            self.log_message("❌ لا يوجد عنصر مختار")
            self.individual_result_var.set("")
        
        self.log_message("=== انتهاء on_result_select ===")
        self.log_message(f"🎯 القيمة النهائية في القائمة المنسدلة: '{self.individual_result_var.get()}'")
        self.log_message("-" * 50)
        
    def apply_individual_result(self):
        """محاكاة تطبيق النتيجة الفردية"""
        self.log_message("=== بداية apply_individual_result ===")
        
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل من الجدول أولاً")
            self.log_message("❌ لم يتم اختيار عنصر")
            return

        result = self.individual_result_var.get()
        if not result or result.strip() == "":
            messagebox.showwarning("تحذير", "يرجى اختيار نتيجة من القائمة المنسدلة")
            self.log_message("❌ لم يتم اختيار نتيجة")
            return

        if self.selected_item_info:
            confirm_msg = f"هل تريد تطبيق النتيجة '{result}' على:\n\n"
            confirm_msg += f"المريض: {self.selected_item_info['patient_name']}\n"
            confirm_msg += f"التحليل: {self.selected_item_info['test_name']}\n"
            confirm_msg += f"الرقم الوطني: {self.selected_item_info['national_id']}"
            
            if messagebox.askyesno("تأكيد تطبيق النتيجة", confirm_msg):
                success_msg = f"✅ تم تطبيق النتيجة بنجاح!\n\n"
                success_msg += f"المريض: {self.selected_item_info['patient_name']}\n"
                success_msg += f"التحليل: {self.selected_item_info['test_name']}\n"
                success_msg += f"النتيجة الجديدة: {result}"
                
                messagebox.showinfo("نجح", success_msg)
                self.log_message(f"✅ تم تطبيق النتيجة '{result}' بنجاح")
            else:
                self.log_message("❌ المستخدم ألغى العملية")
        else:
            self.log_message("❌ لا توجد معلومات للعنصر المختار")
        
        self.log_message("=== انتهاء apply_individual_result ===")
        self.log_message("-" * 50)
        
    def log_message(self, message):
        """إضافة رسالة إلى منطقة المعلومات"""
        self.info_text.insert(tk.END, f"{message}\n")
        self.info_text.see(tk.END)
        
    def run(self):
        """تشغيل الاختبار"""
        self.root.mainloop()

if __name__ == "__main__":
    test_app = TestIndividualResult()
    test_app.run()
