import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from database import DatabaseManager
from data_entry_tab import DataEntryTab
from work_tab import WorkTab
from results_tab import ResultsTab
from reports_tab import ReportsTab
from settings_tab import SettingsTab

class MainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("مختبر الصحة العامة المركزي - ذي قار")
        self.root.geometry("1400x800")
        self.root.configure(bg='#0f1419')

        # إعداد الخطوط العصرية
        self.font_normal = ('Segoe UI', 10)
        self.font_bold = ('Segoe UI', 10, 'bold')
        self.font_large = ('Segoe UI', 12, 'bold')
        self.font_title = ('Segoe UI', 14, 'bold')

        # إعداد الألوان العصرية (Dark Theme)
        self.colors = {
            'primary': '#1e3a8a',      # أزرق عميق عصري
            'secondary': '#1e293b',    # رمادي أزرق داكن
            'accent': '#06b6d4',       # سماوي عصري
            'background': '#0f1419',   # خلفية داكنة جداً
            'surface': '#1e293b',      # سطح داكن
            'card': '#334155',         # بطاقات
            'white': '#ffffff',        # أبيض
            'text': '#f1f5f9',         # نص فاتح
            'text_secondary': '#94a3b8', # نص ثانوي
            'border': '#475569',       # حدود
            'hover': '#3b82f6',        # تمرير
            'success': '#10b981',      # أخضر عصري
            'warning': '#f59e0b',      # برتقالي عصري
            'danger': '#ef4444',       # أحمر عصري
            'tab_active': '#1e3a8a',   # تبويب نشط
            'tab_inactive': '#374151'  # تبويب غير نشط
        }
        
        # إنشاء قاعدة البيانات
        self.db = DatabaseManager()
        
        # إعداد النافذة الرئيسية
        self.setup_main_window()
        
        # إنشاء التبويبات
        self.create_tabs()
        
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        # إعداد الشبكة (عكس الأعمدة لجعل الشريط الجانبي على اليمين)
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)  # المنطقة الرئيسية
        self.root.grid_columnconfigure(1, weight=0)  # الشريط الجانبي

        # إنشاء المنطقة الرئيسية أولاً
        self.create_main_area()

        # إنشاء الشريط الجانبي على اليمين
        self.create_sidebar()

    def create_sidebar(self):
        """إنشاء الشريط الجانبي العصري للتبويبات على اليمين"""
        # إطار الشريط الجانبي مع تدرج لوني
        self.sidebar = tk.Frame(
            self.root,
            bg=self.colors['surface'],
            width=250,
            relief='flat',
            bd=0
        )
        self.sidebar.grid(row=0, column=1, sticky='nsew', padx=(2, 0), pady=0)
        self.sidebar.grid_propagate(False)

        # إطار علوي للعنوان مع تصميم عصري
        header_frame = tk.Frame(self.sidebar, bg=self.colors['primary'], height=100)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)

        # عنوان المختبر مع تصميم عصري
        title_label = tk.Label(
            header_frame,
            text="🏥 مختبر الصحة العامة\nالمركزي - ذي قار",
            font=self.font_title,
            bg=self.colors['primary'],
            fg=self.colors['white'],
            justify='center'
        )
        title_label.pack(expand=True)

        # خط فاصل عصري مع تدرج
        separator = tk.Frame(self.sidebar, bg=self.colors['accent'], height=3)
        separator.pack(fill='x', padx=10, pady=(0, 20))

        # إضافة معلومات النظام في أسفل الشريط الجانبي
        self.add_sidebar_footer()
        
        # أزرار التبويبات العصرية مع أيقونات
        self.tab_buttons = {}
        tabs = [
            ("📝 إدخال البيانات", "data_entry"),
            ("⚡ العمل", "work"),
            ("📊 النتائج", "results"),
            ("📋 التقارير والإحصائيات", "reports"),
            ("⚙️ الإعدادات", "settings")
        ]

        for tab_name, tab_key in tabs:
            # إطار للزر مع تأثيرات عصرية
            btn_frame = tk.Frame(self.sidebar, bg=self.colors['surface'])
            btn_frame.pack(pady=8, padx=15, fill='x')

            btn = tk.Button(
                btn_frame,
                text=tab_name,
                font=self.font_bold,
                bg=self.colors['tab_inactive'],
                fg=self.colors['text'],
                activebackground=self.colors['hover'],
                activeforeground=self.colors['white'],
                relief='flat',
                bd=0,
                width=20,
                height=2,
                cursor='hand2',
                command=lambda key=tab_key: self.switch_tab(key)
            )
            btn.pack(fill='x', ipady=8)

            # تأثيرات التمرير العصرية
            def on_enter(event, button=btn):
                if button['bg'] != self.colors['tab_active']:
                    button.config(bg=self.colors['hover'], fg=self.colors['white'])

            def on_leave(event, button=btn):
                if button['bg'] != self.colors['tab_active']:
                    button.config(bg=self.colors['tab_inactive'], fg=self.colors['text'])

            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)

            self.tab_buttons[tab_key] = btn
        
        # تحديد التبويب الافتراضي
        self.current_tab = "data_entry"
        self.highlight_current_tab()

    def add_sidebar_footer(self):
        """إضافة تذييل عصري للشريط الجانبي"""
        # إطار التذييل في أسفل الشريط الجانبي
        footer_frame = tk.Frame(self.sidebar, bg=self.colors['surface'])
        footer_frame.pack(side='bottom', fill='x', padx=10, pady=20)

        # معلومات النظام
        info_label = tk.Label(
            footer_frame,
            text="نظام إدارة المختبر v2.0\n© 2024",
            font=('Segoe UI', 8),
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            justify='center'
        )
        info_label.pack()

        # خط فاصل صغير
        mini_separator = tk.Frame(footer_frame, bg=self.colors['border'], height=1)
        mini_separator.pack(fill='x', pady=(10, 5))

        # حالة الاتصال
        status_label = tk.Label(
            footer_frame,
            text="🟢 متصل",
            font=('Segoe UI', 8),
            bg=self.colors['surface'],
            fg=self.colors['success'],
            justify='center'
        )
        status_label.pack()
        
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية العصرية للمحتوى"""
        # المنطقة الرئيسية مع خلفية عصرية
        self.main_frame = tk.Frame(
            self.root,
            bg=self.colors['background'],
            relief='flat',
            bd=0
        )
        self.main_frame.grid(row=0, column=0, sticky='nsew', padx=0, pady=0)

        # شريط علوي عصري للعنوان
        header_bar = tk.Frame(self.main_frame, bg=self.colors['card'], height=60)
        header_bar.pack(fill='x', padx=0, pady=0)
        header_bar.pack_propagate(False)

        # عنوان الصفحة الحالية
        self.page_title = tk.Label(
            header_bar,
            text="📝 إدخال البيانات",
            font=self.font_title,
            bg=self.colors['card'],
            fg=self.colors['white'],
            anchor='w'
        )
        self.page_title.pack(side='right', padx=20, pady=15)

        # إطار للمحتوى مع تصميم عصري
        self.content_frame = tk.Frame(
            self.main_frame,
            bg=self.colors['background'],
            relief='flat',
            bd=0
        )
        self.content_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
    def create_tabs(self):
        """إنشاء جميع التبويبات"""
        self.tabs = {}
        
        # تبويب إدخال البيانات
        self.tabs['data_entry'] = DataEntryTab(self.content_frame, self.db, self.colors, self.font_normal)
        
        # تبويب العمل
        self.tabs['work'] = WorkTab(self.content_frame, self.db, self.colors, self.font_normal)
        
        # تبويب النتائج
        self.tabs['results'] = ResultsTab(self.content_frame, self.db, self.colors, self.font_normal)
        
        # تبويب التقارير
        self.tabs['reports'] = ReportsTab(self.content_frame, self.db, self.colors, self.font_normal)
        
        # تبويب الإعدادات
        self.tabs['settings'] = SettingsTab(self.content_frame, self.db, self.colors, self.font_normal)
        
        # إخفاء جميع التبويبات عدا الأول
        for key, tab in self.tabs.items():
            if key != self.current_tab:
                tab.hide()
        
    def switch_tab(self, tab_key):
        """تبديل التبويبات مع تحديث العنوان"""
        if tab_key == self.current_tab:
            return

        # إخفاء التبويب الحالي
        self.tabs[self.current_tab].hide()

        # إظهار التبويب الجديد
        self.tabs[tab_key].show()

        # تحديث التبويب الحالي
        self.current_tab = tab_key

        # تحديث تمييز الأزرار
        self.highlight_current_tab()

        # تحديث عنوان الصفحة
        self.update_page_title(tab_key)

        # تحديث البيانات في التبويب الجديد
        self.tabs[tab_key].refresh_data()

    def update_page_title(self, tab_key):
        """تحديث عنوان الصفحة الحالية"""
        titles = {
            'data_entry': "📝 إدخال البيانات",
            'work': "⚡ العمل",
            'results': "📊 النتائج",
            'reports': "📋 التقارير والإحصائيات",
            'settings': "⚙️ الإعدادات"
        }
        self.page_title.config(text=titles.get(tab_key, ""))

    def highlight_current_tab(self):
        """تمييز التبويب الحالي بتصميم عصري"""
        for key, btn in self.tab_buttons.items():
            if key == self.current_tab:
                btn.configure(
                    bg=self.colors['tab_active'],
                    fg=self.colors['white'],
                    relief='flat'
                )
            else:
                btn.configure(
                    bg=self.colors['tab_inactive'],
                    fg=self.colors['text'],
                    relief='flat'
                )
    
    def refresh_all_tabs(self):
        """تحديث جميع التبويبات"""
        for tab in self.tabs.values():
            tab.refresh_data()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = MainWindow()
    app.run()
