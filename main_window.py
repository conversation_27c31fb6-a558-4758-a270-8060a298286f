import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from database import DatabaseManager
from data_entry_tab import DataEntryTab
from work_tab import WorkTab
from results_tab import ResultsTab
from reports_tab import ReportsTab
from settings_tab import SettingsTab

class MainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("مختبر الصحة العامة المركزي - ذي قار")
        self.root.geometry("1400x800")
        self.root.configure(bg='#f0f0f0')
        
        # إعداد الخطوط
        self.font_normal = ('Arial', 10)
        self.font_bold = ('Arial', 10, 'bold')
        
        # إعداد الألوان
        self.colors = {
            'primary': '#2E86AB',      # أزرق
            'secondary': '#A23B72',    # بنفسجي
            'background': '#F18F01',   # برتقالي فاتح
            'white': '#FFFFFF',        # أبيض
            'gray': '#E5E5E5',         # رمادي فاتح
            'dark_gray': '#CCCCCC',    # رمادي داكن
            'text': '#333333'          # نص داكن
        }
        
        # إنشاء قاعدة البيانات
        self.db = DatabaseManager()
        
        # إعداد النافذة الرئيسية
        self.setup_main_window()
        
        # إنشاء التبويبات
        self.create_tabs()
        
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        # إعداد الشبكة
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(1, weight=1)
        
        # إنشاء الشريط الجانبي
        self.create_sidebar()
        
        # إنشاء المنطقة الرئيسية
        self.create_main_area()
        
    def create_sidebar(self):
        """إنشاء الشريط الجانبي للتبويبات"""
        self.sidebar = tk.Frame(self.root, bg=self.colors['primary'], width=200)
        self.sidebar.grid(row=0, column=0, sticky='nsew', padx=5, pady=5)
        self.sidebar.grid_propagate(False)
        
        # عنوان المختبر
        title_label = tk.Label(
            self.sidebar,
            text="مختبر الصحة العامة\nالمركزي - ذي قار",
            font=('Arial', 12, 'bold'),
            bg=self.colors['primary'],
            fg=self.colors['white'],
            justify='center'
        )
        title_label.pack(pady=20)
        
        # أزرار التبويبات
        self.tab_buttons = {}
        tabs = [
            ("إدخال البيانات", "data_entry"),
            ("العمل", "work"),
            ("النتائج", "results"),
            ("التقارير والإحصائيات", "reports"),
            ("الإعدادات", "settings")
        ]
        
        for tab_name, tab_key in tabs:
            btn = tk.Button(
                self.sidebar,
                text=tab_name,
                font=self.font_bold,
                bg=self.colors['white'],
                fg=self.colors['text'],
                relief='raised',
                bd=3,
                width=18,
                height=2,
                command=lambda key=tab_key: self.switch_tab(key)
            )
            btn.pack(pady=5, padx=10, fill='x')
            self.tab_buttons[tab_key] = btn
        
        # تحديد التبويب الافتراضي
        self.current_tab = "data_entry"
        self.highlight_current_tab()
        
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية للمحتوى"""
        self.main_frame = tk.Frame(self.root, bg=self.colors['white'])
        self.main_frame.grid(row=0, column=1, sticky='nsew', padx=5, pady=5)
        
        # إطار للمحتوى
        self.content_frame = tk.Frame(self.main_frame, bg=self.colors['white'])
        self.content_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
    def create_tabs(self):
        """إنشاء جميع التبويبات"""
        self.tabs = {}
        
        # تبويب إدخال البيانات
        self.tabs['data_entry'] = DataEntryTab(self.content_frame, self.db, self.colors, self.font_normal)
        
        # تبويب العمل
        self.tabs['work'] = WorkTab(self.content_frame, self.db, self.colors, self.font_normal)
        
        # تبويب النتائج
        self.tabs['results'] = ResultsTab(self.content_frame, self.db, self.colors, self.font_normal)
        
        # تبويب التقارير
        self.tabs['reports'] = ReportsTab(self.content_frame, self.db, self.colors, self.font_normal)
        
        # تبويب الإعدادات
        self.tabs['settings'] = SettingsTab(self.content_frame, self.db, self.colors, self.font_normal)
        
        # إخفاء جميع التبويبات عدا الأول
        for key, tab in self.tabs.items():
            if key != self.current_tab:
                tab.hide()
        
    def switch_tab(self, tab_key):
        """تبديل التبويبات"""
        if tab_key == self.current_tab:
            return
            
        # إخفاء التبويب الحالي
        self.tabs[self.current_tab].hide()
        
        # إظهار التبويب الجديد
        self.tabs[tab_key].show()
        
        # تحديث التبويب الحالي
        self.current_tab = tab_key
        
        # تحديث تمييز الأزرار
        self.highlight_current_tab()
        
        # تحديث البيانات في التبويب الجديد
        self.tabs[tab_key].refresh_data()
        
    def highlight_current_tab(self):
        """تمييز التبويب الحالي"""
        for key, btn in self.tab_buttons.items():
            if key == self.current_tab:
                btn.configure(
                    bg=self.colors['background'],
                    fg=self.colors['white'],
                    relief='sunken'
                )
            else:
                btn.configure(
                    bg=self.colors['white'],
                    fg=self.colors['text'],
                    relief='raised'
                )
    
    def refresh_all_tabs(self):
        """تحديث جميع التبويبات"""
        for tab in self.tabs.values():
            tab.refresh_data()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = MainWindow()
    app.run()
