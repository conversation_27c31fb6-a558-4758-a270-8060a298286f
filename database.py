import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_name="lab_database.db"):
        self.db_name = db_name
        self.init_database()
    
    def get_connection(self):
        return sqlite3.connect(self.db_name)
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المرضى والعينات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                national_id INTEGER UNIQUE,
                name TEXT NOT NULL,
                age INTEGER NOT NULL,
                gender TEXT NOT NULL CHECK (gender IN ('M', 'F')),
                address TEXT NOT NULL,
                phone TEXT NOT NULL,
                passport_number TEXT,
                receipt_number TEXT,
                sample_type TEXT NOT NULL,
                sender_organization TEXT NOT NULL,
                sample_collection_date DATE NOT NULL,
                sample_received_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول التحاليل المطلوبة لكل مريض
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patient_tests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                test_name TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول الوجبات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS batches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_number INTEGER UNIQUE,
                start_date DATE,
                end_date DATE,
                work_date DATE DEFAULT CURRENT_DATE,
                status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'sent')),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول عينات الوجبة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS batch_samples (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_id INTEGER,
                patient_id INTEGER,
                technician_name TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (batch_id) REFERENCES batches (id),
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول النتائج
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_id INTEGER,
                patient_id INTEGER,
                test_name TEXT,
                result TEXT CHECK (result IN ('Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND')),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (batch_id) REFERENCES batches (id),
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول أنواع العينات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sample_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول جهات الإرسال
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sender_organizations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول التحاليل المتاحة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS available_tests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفنيين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS technicians (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج بيانات افتراضية
        self.insert_default_data(cursor)
        
        conn.commit()
        conn.close()
    
    def insert_default_data(self, cursor):
        """إدراج بيانات افتراضية"""
        # أنواع العينات الافتراضية
        sample_types = ['دم', 'بول', 'براز', 'مسحة', 'بلغم', 'سائل نخاعي']
        for sample_type in sample_types:
            cursor.execute('INSERT OR IGNORE INTO sample_types (name) VALUES (?)', (sample_type,))
        
        # جهات الإرسال الافتراضية
        organizations = ['مستشفى الناصرية العام', 'مستشفى الحبوبي', 'مركز صحي الشطرة', 'مستشفى سوق الشيوخ']
        for org in organizations:
            cursor.execute('INSERT OR IGNORE INTO sender_organizations (name) VALUES (?)', (org,))
        
        # التحاليل الافتراضية
        tests = ['فحص كوفيد-19', 'فحص الملاريا', 'فحص السل', 'فحص الكوليرا', 'فحص التيفوئيد']
        for test in tests:
            cursor.execute('INSERT OR IGNORE INTO available_tests (name) VALUES (?)', (test,))
        
        # الفنيين الافتراضيين
        technicians = ['أحمد محمد', 'فاطمة علي', 'محمد حسن', 'زينب أحمد']
        for tech in technicians:
            cursor.execute('INSERT OR IGNORE INTO technicians (name) VALUES (?)', (tech,))
    
    def get_next_national_id(self):
        """الحصول على الرقم الوطني التالي"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT MAX(national_id) FROM patients')
        result = cursor.fetchone()
        conn.close()
        return (result[0] + 1) if result[0] else 1
    
    def get_next_batch_number(self):
        """الحصول على رقم الوجبة التالي"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT MAX(batch_number) FROM batches')
        result = cursor.fetchone()
        conn.close()
        return (result[0] + 1) if result[0] else 1
