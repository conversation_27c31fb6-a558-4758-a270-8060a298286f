@echo off
chcp 65001 > nul
title مختبر الصحة العامة المركزي - ذي قار

echo ========================================
echo    مختبر الصحة العامة المركزي - ذي قار
echo ========================================
echo.

echo جاري التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تحميل وتثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo جاري التحقق من المكتبات المطلوبة...
python -c "import openpyxl, reportlab, PIL, win32print" > nul 2>&1
if errorlevel 1 (
    echo تثبيت المكتبات المطلوبة...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo خطأ في تثبيت المكتبات
        echo يرجى تشغيل الأمر التالي يدوياً:
        echo pip install openpyxl reportlab Pillow pywin32
        pause
        exit /b 1
    )
)

echo جاري إعداد وتشغيل البرنامج...
echo.
python setup_and_run.py

if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل البرنامج
    echo تحقق من الأخطاء أعلاه
    pause
) else (
    echo.
    echo تم إغلاق البرنامج بنجاح
)
