import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sqlite3

class WorkTab:
    def __init__(self, parent, db, colors, font):
        self.parent = parent
        self.db = db
        self.colors = colors
        self.font = font
        
        self.create_widgets()
        self.current_batch_id = None
        
    def create_widgets(self):
        """إنشاء عناصر واجهة تبويب العمل"""
        # الإطار الرئيسي
        self.main_frame = tk.Frame(self.parent, bg=self.colors['white'])
        
        # عنوان التبويب
        title_label = tk.Label(
            self.main_frame,
            text="إدارة وجبات العمل",
            font=('Arial', 14, 'bold'),
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        title_label.pack(pady=10)
        
        # إطار إنشاء الوجبة
        self.create_batch_frame()
        
        # إطار عرض الوجبات
        self.create_batches_list_frame()
        
        # إطار تفاصيل الوجبة
        self.create_batch_details_frame()
        
        # تحديث البيانات
        self.refresh_data()
        
    def create_batch_frame(self):
        """إنشاء إطار إنشاء الوجبة الجديدة"""
        batch_frame = tk.LabelFrame(
            self.main_frame,
            text="إنشاء وجبة عمل جديدة",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        batch_frame.pack(fill='x', padx=20, pady=10)
        
        # إطار للحقول
        fields_frame = tk.Frame(batch_frame, bg=self.colors['white'])
        fields_frame.pack(fill='x', padx=10, pady=10)
        
        # رقم الوجبة (تلقائي)
        tk.Label(fields_frame, text="رقم الوجبة:", font=self.font, bg=self.colors['white']).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.batch_number_var = tk.StringVar()
        batch_number_entry = tk.Entry(fields_frame, textvariable=self.batch_number_var, state='readonly', width=15)
        batch_number_entry.grid(row=0, column=1, sticky='w', padx=5, pady=5)
        
        # تاريخ البداية
        tk.Label(fields_frame, text="من تاريخ:", font=self.font, bg=self.colors['white']).grid(row=0, column=2, sticky='w', padx=5, pady=5)
        self.start_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        start_date_entry = tk.Entry(fields_frame, textvariable=self.start_date_var, width=15)
        start_date_entry.grid(row=0, column=3, sticky='w', padx=5, pady=5)
        
        # تاريخ النهاية
        tk.Label(fields_frame, text="إلى تاريخ:", font=self.font, bg=self.colors['white']).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.end_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        end_date_entry = tk.Entry(fields_frame, textvariable=self.end_date_var, width=15)
        end_date_entry.grid(row=1, column=1, sticky='w', padx=5, pady=5)
        
        # تاريخ العمل (تلقائي)
        tk.Label(fields_frame, text="تاريخ العمل:", font=self.font, bg=self.colors['white']).grid(row=1, column=2, sticky='w', padx=5, pady=5)
        self.work_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        work_date_entry = tk.Entry(fields_frame, textvariable=self.work_date_var, state='readonly', width=15)
        work_date_entry.grid(row=1, column=3, sticky='w', padx=5, pady=5)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(batch_frame, bg=self.colors['white'])
        buttons_frame.pack(fill='x', padx=10, pady=10)
        
        create_btn = tk.Button(
            buttons_frame,
            text="إنشاء الوجبة",
            command=self.create_batch,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=15,
            height=2
        )
        create_btn.pack(side='left', padx=5)
        
        clear_btn = tk.Button(
            buttons_frame,
            text="مسح الحقول",
            command=self.clear_batch_fields,
            font=self.font,
            bg=self.colors['dark_gray'],
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=15,
            height=2
        )
        clear_btn.pack(side='left', padx=5)
        
    def create_batches_list_frame(self):
        """إنشاء إطار قائمة الوجبات"""
        list_frame = tk.LabelFrame(
            self.main_frame,
            text="الوجبات المتاحة",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        list_frame.pack(fill='x', padx=20, pady=10)
        
        # جدول الوجبات
        columns = ('رقم الوجبة', 'من تاريخ', 'إلى تاريخ', 'تاريخ العمل', 'عدد العينات', 'الحالة')
        self.batches_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=6)
        
        for col in columns:
            self.batches_tree.heading(col, text=col)
            self.batches_tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        batches_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.batches_tree.yview)
        self.batches_tree.configure(yscrollcommand=batches_scrollbar.set)
        
        # تخطيط الجدول
        self.batches_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        batches_scrollbar.pack(side='right', fill='y', pady=10)
        
        # ربط حدث النقر
        self.batches_tree.bind('<ButtonRelease-1>', self.on_batch_select)
        
    def create_batch_details_frame(self):
        """إنشاء إطار تفاصيل الوجبة"""
        details_frame = tk.LabelFrame(
            self.main_frame,
            text="تفاصيل الوجبة المحددة",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        details_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # معلومات الوجبة
        info_frame = tk.Frame(details_frame, bg=self.colors['white'])
        info_frame.pack(fill='x', padx=10, pady=5)
        
        self.batch_info_label = tk.Label(
            info_frame,
            text="اختر وجبة لعرض التفاصيل",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        self.batch_info_label.pack(side='left')
        
        # أزرار العمليات
        actions_frame = tk.Frame(info_frame, bg=self.colors['white'])
        actions_frame.pack(side='right')
        
        self.send_to_results_btn = tk.Button(
            actions_frame,
            text="إرسال إلى النتائج",
            command=self.send_batch_to_results,
            font=self.font,
            bg='#28A745',
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=15,
            height=2,
            state='disabled'
        )
        self.send_to_results_btn.pack(side='right', padx=5)
        
        # جدول عينات الوجبة
        samples_columns = ('الرقم الوطني', 'اسم المريض', 'نوع العينة', 'الفني المسؤول', 'التحاليل')
        self.samples_tree = ttk.Treeview(details_frame, columns=samples_columns, show='headings', height=8)
        
        for col in samples_columns:
            self.samples_tree.heading(col, text=col)
            self.samples_tree.column(col, width=150, anchor='center')
        
        # شريط التمرير للعينات
        samples_scrollbar = ttk.Scrollbar(details_frame, orient='vertical', command=self.samples_tree.yview)
        self.samples_tree.configure(yscrollcommand=samples_scrollbar.set)
        
        # تخطيط جدول العينات
        self.samples_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        samples_scrollbar.pack(side='right', fill='y', pady=10)
        
        # إطار تحديد الفنيين
        technician_frame = tk.Frame(details_frame, bg=self.colors['white'])
        technician_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(technician_frame, text="تحديد الفني:", font=self.font, bg=self.colors['white']).pack(side='left')
        
        self.technician_var = tk.StringVar()
        self.technician_combo = ttk.Combobox(
            technician_frame,
            textvariable=self.technician_var,
            font=self.font,
            width=20,
            state='readonly'
        )
        self.technician_combo.pack(side='left', padx=5)
        
        assign_btn = tk.Button(
            technician_frame,
            text="تحديد للعينة المختارة",
            command=self.assign_technician,
            font=self.font,
            bg=self.colors['background'],
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        assign_btn.pack(side='left', padx=5)
        
        assign_all_btn = tk.Button(
            technician_frame,
            text="تحديد لجميع العينات",
            command=self.assign_technician_to_all,
            font=self.font,
            bg=self.colors['secondary'],
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        assign_all_btn.pack(side='left', padx=5)
        
        # ربط حدث النقر على العينات
        self.samples_tree.bind('<ButtonRelease-1>', self.on_sample_select)
        
    def create_batch(self):
        """إنشاء وجبة عمل جديدة"""
        try:
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()
            
            if not start_date or not end_date:
                messagebox.showerror("خطأ", "يرجى إدخال تاريخ البداية والنهاية")
                return
            
            # التحقق من صحة التواريخ
            try:
                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                
                if start_dt > end_dt:
                    messagebox.showerror("خطأ", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
                    return
                    
            except ValueError:
                messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح (YYYY-MM-DD)")
                return
            
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # الحصول على رقم الوجبة التالي
            batch_number = self.db.get_next_batch_number()
            
            # إنشاء الوجبة
            cursor.execute('''
                INSERT INTO batches (batch_number, start_date, end_date, work_date)
                VALUES (?, ?, ?, ?)
            ''', (batch_number, start_date, end_date, self.work_date_var.get()))
            
            batch_id = cursor.lastrowid
            
            # إضافة العينات للوجبة
            cursor.execute('''
                SELECT id FROM patients 
                WHERE sample_collection_date BETWEEN ? AND ?
                AND id NOT IN (SELECT patient_id FROM batch_samples)
            ''', (start_date, end_date))
            
            patient_ids = [row[0] for row in cursor.fetchall()]
            
            if not patient_ids:
                messagebox.showwarning("تحذير", "لا توجد عينات في الفترة المحددة أو جميع العينات مضافة لوجبات أخرى")
                cursor.execute('DELETE FROM batches WHERE id=?', (batch_id,))
                conn.commit()
                conn.close()
                return
            
            # إضافة العينات للوجبة
            for patient_id in patient_ids:
                cursor.execute('''
                    INSERT INTO batch_samples (batch_id, patient_id)
                    VALUES (?, ?)
                ''', (batch_id, patient_id))
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("نجح", f"تم إنشاء الوجبة رقم {batch_number} بنجاح\nعدد العينات: {len(patient_ids)}")
            
            # تحديث البيانات ومسح الحقول
            self.refresh_data()
            self.clear_batch_fields()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الوجبة: {str(e)}")
    
    def clear_batch_fields(self):
        """مسح حقول إنشاء الوجبة"""
        self.start_date_var.set(datetime.now().strftime("%Y-%m-%d"))
        self.end_date_var.set(datetime.now().strftime("%Y-%m-%d"))
        self.work_date_var.set(datetime.now().strftime("%Y-%m-%d"))
        
        # تحديث رقم الوجبة التالي
        next_batch = self.db.get_next_batch_number()
        self.batch_number_var.set(next_batch)

    def on_batch_select(self, event):
        """معالج حدث اختيار وجبة"""
        selection = self.batches_tree.selection()
        if selection:
            item = self.batches_tree.item(selection[0])
            batch_number = item['values'][0]
            self.load_batch_details(batch_number)

    def load_batch_details(self, batch_number):
        """تحميل تفاصيل الوجبة"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # الحصول على معلومات الوجبة
            cursor.execute('''
                SELECT id, batch_number, start_date, end_date, work_date, status
                FROM batches WHERE batch_number=?
            ''', (batch_number,))

            batch_info = cursor.fetchone()

            if batch_info:
                self.current_batch_id = batch_info[0]

                # تحديث معلومات الوجبة
                info_text = f"الوجبة رقم {batch_info[1]} - من {batch_info[2]} إلى {batch_info[3]} - تاريخ العمل: {batch_info[4]} - الحالة: {batch_info[5]}"
                self.batch_info_label.config(text=info_text)

                # تفعيل زر الإرسال إذا كانت الوجبة نشطة
                if batch_info[5] == 'active':
                    self.send_to_results_btn.config(state='normal')
                else:
                    self.send_to_results_btn.config(state='disabled')

                # تحميل عينات الوجبة
                self.load_batch_samples()

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل تفاصيل الوجبة: {str(e)}")

    def load_batch_samples(self):
        """تحميل عينات الوجبة"""
        # مسح البيانات الحالية
        for item in self.samples_tree.get_children():
            self.samples_tree.delete(item)

        if not self.current_batch_id:
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT p.national_id, p.name, p.sample_type, bs.technician_name,
                       GROUP_CONCAT(pt.test_name, ', ') as tests
                FROM batch_samples bs
                JOIN patients p ON bs.patient_id = p.id
                LEFT JOIN patient_tests pt ON p.id = pt.patient_id
                WHERE bs.batch_id = ?
                GROUP BY p.id
                ORDER BY p.national_id
            ''', (self.current_batch_id,))

            for row in cursor.fetchall():
                technician = row[3] if row[3] else "غير محدد"
                tests = row[4] if row[4] else "لا توجد تحاليل"
                self.samples_tree.insert('', 'end', values=(row[0], row[1], row[2], technician, tests))

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل عينات الوجبة: {str(e)}")

    def on_sample_select(self, event):
        """معالج حدث اختيار عينة"""
        # يمكن إضافة وظائف إضافية هنا إذا لزم الأمر
        pass

    def assign_technician(self):
        """تحديد فني للعينة المختارة"""
        selection = self.samples_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عينة")
            return

        technician = self.technician_var.get()
        if not technician:
            messagebox.showwarning("تحذير", "يرجى اختيار فني")
            return

        try:
            item = self.samples_tree.item(selection[0])
            national_id = item['values'][0]

            conn = self.db.get_connection()
            cursor = conn.cursor()

            # تحديث الفني للعينة
            cursor.execute('''
                UPDATE batch_samples
                SET technician_name = ?
                WHERE batch_id = ? AND patient_id = (
                    SELECT id FROM patients WHERE national_id = ?
                )
            ''', (technician, self.current_batch_id, national_id))

            conn.commit()
            conn.close()

            # تحديث العرض
            self.load_batch_samples()
            messagebox.showinfo("نجح", "تم تحديد الفني بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديد الفني: {str(e)}")

    def assign_technician_to_all(self):
        """تحديد فني لجميع عينات الوجبة"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وجبة")
            return

        technician = self.technician_var.get()
        if not technician:
            messagebox.showwarning("تحذير", "يرجى اختيار فني")
            return

        if messagebox.askyesno("تأكيد", f"هل تريد تحديد الفني '{technician}' لجميع عينات الوجبة؟"):
            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # تحديث الفني لجميع عينات الوجبة
                cursor.execute('''
                    UPDATE batch_samples
                    SET technician_name = ?
                    WHERE batch_id = ?
                ''', (technician, self.current_batch_id))

                conn.commit()
                conn.close()

                # تحديث العرض
                self.load_batch_samples()
                messagebox.showinfo("نجح", "تم تحديد الفني لجميع العينات بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديد الفني: {str(e)}")

    def send_batch_to_results(self):
        """إرسال الوجبة إلى تبويب النتائج"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وجبة")
            return

        # التحقق من تحديد الفنيين لجميع العينات
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT COUNT(*) FROM batch_samples
                WHERE batch_id = ? AND (technician_name IS NULL OR technician_name = '')
            ''', (self.current_batch_id,))

            unassigned_count = cursor.fetchone()[0]

            if unassigned_count > 0:
                if not messagebox.askyesno("تحذير", f"هناك {unassigned_count} عينة لم يتم تحديد فني لها. هل تريد المتابعة؟"):
                    conn.close()
                    return

            # تحديث حالة الوجبة إلى مكتملة
            cursor.execute('''
                UPDATE batches
                SET status = 'completed'
                WHERE id = ?
            ''', (self.current_batch_id,))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إرسال الوجبة إلى تبويب النتائج بنجاح")

            # تحديث البيانات
            self.refresh_data()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إرسال الوجبة: {str(e)}")

    def refresh_data(self):
        """تحديث جميع البيانات"""
        # تحديث قائمة الوجبات
        self.refresh_batches_list()

        # تحديث قائمة الفنيين
        self.refresh_technicians_list()

        # تحديث رقم الوجبة التالي
        next_batch = self.db.get_next_batch_number()
        self.batch_number_var.set(next_batch)

    def refresh_batches_list(self):
        """تحديث قائمة الوجبات"""
        # مسح البيانات الحالية
        for item in self.batches_tree.get_children():
            self.batches_tree.delete(item)

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT b.batch_number, b.start_date, b.end_date, b.work_date,
                       COUNT(bs.patient_id) as sample_count, b.status
                FROM batches b
                LEFT JOIN batch_samples bs ON b.id = bs.batch_id
                GROUP BY b.id
                ORDER BY b.batch_number DESC
            ''')

            for row in cursor.fetchall():
                status_text = {
                    'active': 'نشط',
                    'completed': 'مكتمل',
                    'sent': 'مرسل'
                }.get(row[5], row[5])

                display_row = (row[0], row[1], row[2], row[3], row[4], status_text)
                self.batches_tree.insert('', 'end', values=display_row)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث قائمة الوجبات: {str(e)}")

    def refresh_technicians_list(self):
        """تحديث قائمة الفنيين"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT name FROM technicians ORDER BY name')
            technicians = [row[0] for row in cursor.fetchall()]

            self.technician_combo['values'] = technicians

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث قائمة الفنيين: {str(e)}")

    def show(self):
        """إظهار التبويب"""
        self.main_frame.pack(fill='both', expand=True)

    def hide(self):
        """إخفاء التبويب"""
        self.main_frame.pack_forget()
