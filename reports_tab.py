import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import sqlite3
from report_generator import ReportGenerator

class ReportsTab:
    def __init__(self, parent, db, colors, font):
        self.parent = parent
        self.db = db
        self.colors = colors
        self.font = font
        self.report_generator = ReportGenerator(db)
        
        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء عناصر واجهة تبويب التقارير"""
        # الإطار الرئيسي
        self.main_frame = tk.Frame(self.parent, bg=self.colors['white'])
        
        # عنوان التبويب
        title_label = tk.Label(
            self.main_frame,
            text="التقارير والإحصائيات",
            font=('Arial', 14, 'bold'),
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        title_label.pack(pady=10)
        
        # إطار الفلاتر
        self.create_filters_frame()
        
        # إطار النتائج
        self.create_results_frame()
        
        # تحديث البيانات
        self.refresh_data()
        
    def create_filters_frame(self):
        """إنشاء إطار الفلاتر"""
        filters_frame = tk.LabelFrame(
            self.main_frame,
            text="فلاتر البحث",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        filters_frame.pack(fill='x', padx=20, pady=10)
        
        # الصف الأول من الفلاتر
        row1_frame = tk.Frame(filters_frame, bg=self.colors['white'])
        row1_frame.pack(fill='x', padx=10, pady=5)
        
        # فترة التاريخ
        tk.Label(row1_frame, text="من تاريخ:", font=self.font, bg=self.colors['white']).grid(row=0, column=0, sticky='w', padx=5)
        self.start_date_var = tk.StringVar(value=(datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"))
        start_date_entry = tk.Entry(row1_frame, textvariable=self.start_date_var, width=12)
        start_date_entry.grid(row=0, column=1, padx=5)
        
        tk.Label(row1_frame, text="إلى تاريخ:", font=self.font, bg=self.colors['white']).grid(row=0, column=2, sticky='w', padx=5)
        self.end_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        end_date_entry = tk.Entry(row1_frame, textvariable=self.end_date_var, width=12)
        end_date_entry.grid(row=0, column=3, padx=5)
        
        # البحث بالاسم
        tk.Label(row1_frame, text="الاسم:", font=self.font, bg=self.colors['white']).grid(row=0, column=4, sticky='w', padx=5)
        self.name_var = tk.StringVar()
        name_entry = tk.Entry(row1_frame, textvariable=self.name_var, width=15)
        name_entry.grid(row=0, column=5, padx=5)
        
        # الصف الثاني من الفلاتر
        row2_frame = tk.Frame(filters_frame, bg=self.colors['white'])
        row2_frame.pack(fill='x', padx=10, pady=5)
        
        # نوع العينة
        tk.Label(row2_frame, text="نوع العينة:", font=self.font, bg=self.colors['white']).grid(row=0, column=0, sticky='w', padx=5)
        self.sample_type_var = tk.StringVar()
        self.sample_type_combo = ttk.Combobox(row2_frame, textvariable=self.sample_type_var, width=12, state='readonly')
        self.sample_type_combo.grid(row=0, column=1, padx=5)
        
        # الجنس
        tk.Label(row2_frame, text="الجنس:", font=self.font, bg=self.colors['white']).grid(row=0, column=2, sticky='w', padx=5)
        self.gender_var = tk.StringVar()
        gender_combo = ttk.Combobox(row2_frame, textvariable=self.gender_var, values=['', 'M', 'F'], width=8, state='readonly')
        gender_combo.grid(row=0, column=3, padx=5)
        
        # جهة الإرسال
        tk.Label(row2_frame, text="جهة الإرسال:", font=self.font, bg=self.colors['white']).grid(row=0, column=4, sticky='w', padx=5)
        self.sender_org_var = tk.StringVar()
        self.sender_org_combo = ttk.Combobox(row2_frame, textvariable=self.sender_org_var, width=15, state='readonly')
        self.sender_org_combo.grid(row=0, column=5, padx=5)
        
        # الصف الثالث - أزرار العمليات
        row3_frame = tk.Frame(filters_frame, bg=self.colors['white'])
        row3_frame.pack(fill='x', padx=10, pady=10)
        
        # أزرار البحث والفلترة
        search_btn = tk.Button(
            row3_frame,
            text="بحث",
            command=self.search_data,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=10
        )
        search_btn.pack(side='left', padx=5)
        
        clear_btn = tk.Button(
            row3_frame,
            text="مسح الفلاتر",
            command=self.clear_filters,
            font=self.font,
            bg=self.colors['dark_gray'],
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=10
        )
        clear_btn.pack(side='left', padx=5)
        
        # أزرار التقارير
        individual_report_btn = tk.Button(
            row3_frame,
            text="تقرير فردي",
            command=self.generate_individual_report,
            font=self.font,
            bg='#28A745',
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=12
        )
        individual_report_btn.pack(side='left', padx=5)
        
        group_report_btn = tk.Button(
            row3_frame,
            text="تقرير جماعي",
            command=self.generate_group_report,
            font=self.font,
            bg='#17A2B8',
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=12
        )
        group_report_btn.pack(side='left', padx=5)
        
        statistics_btn = tk.Button(
            row3_frame,
            text="إحصائيات",
            command=self.show_statistics,
            font=self.font,
            bg='#6F42C1',
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=10
        )
        statistics_btn.pack(side='left', padx=5)
        
        # البحث بالباركود
        tk.Label(row3_frame, text="الباركود:", font=self.font, bg=self.colors['white']).pack(side='left', padx=(20, 5))
        self.barcode_var = tk.StringVar()
        barcode_entry = tk.Entry(row3_frame, textvariable=self.barcode_var, width=15)
        barcode_entry.pack(side='left', padx=5)
        
        barcode_search_btn = tk.Button(
            row3_frame,
            text="بحث بالباركود",
            command=self.search_by_barcode,
            font=self.font,
            bg='#FFC107',
            fg=self.colors['text'],
            relief='raised',
            bd=3,
            width=12
        )
        barcode_search_btn.pack(side='left', padx=5)
        
    def create_results_frame(self):
        """إنشاء إطار النتائج"""
        results_frame = tk.LabelFrame(
            self.main_frame,
            text="نتائج البحث",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        results_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # معلومات النتائج
        info_frame = tk.Frame(results_frame, bg=self.colors['white'])
        info_frame.pack(fill='x', padx=10, pady=5)
        
        self.results_info_label = tk.Label(
            info_frame,
            text="عدد النتائج: 0",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        self.results_info_label.pack(side='left')
        
        # جدول النتائج
        columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة', 'جهة الإرسال', 'تاريخ السحب', 'التحاليل', 'النتائج')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            if col in ['الاسم', 'جهة الإرسال']:
                self.results_tree.column(col, width=150, anchor='center')
            elif col in ['التحاليل', 'النتائج']:
                self.results_tree.column(col, width=200, anchor='center')
            else:
                self.results_tree.column(col, width=100, anchor='center')
        
        # شريط التمرير
        results_scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=results_scrollbar.set)
        
        # تخطيط الجدول
        self.results_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        results_scrollbar.pack(side='right', fill='y', pady=10)
        
        # ربط حدث النقر المزدوج
        self.results_tree.bind('<Double-1>', self.on_double_click)
        
    def search_data(self):
        """البحث في البيانات"""
        try:
            # بناء استعلام البحث
            query = '''
                SELECT p.national_id, p.name, p.age, p.gender, p.sample_type, 
                       p.sender_organization, p.sample_collection_date,
                       GROUP_CONCAT(DISTINCT pt.test_name) as tests,
                       GROUP_CONCAT(DISTINCT tr.result) as results
                FROM patients p
                LEFT JOIN patient_tests pt ON p.id = pt.patient_id
                LEFT JOIN test_results tr ON (p.id = tr.patient_id AND pt.test_name = tr.test_name)
                WHERE 1=1
            '''
            
            params = []
            
            # فلتر التاريخ
            if self.start_date_var.get():
                query += " AND p.sample_collection_date >= ?"
                params.append(self.start_date_var.get())
            
            if self.end_date_var.get():
                query += " AND p.sample_collection_date <= ?"
                params.append(self.end_date_var.get())
            
            # فلتر الاسم
            if self.name_var.get().strip():
                query += " AND p.name LIKE ?"
                params.append(f"%{self.name_var.get().strip()}%")
            
            # فلتر نوع العينة
            if self.sample_type_var.get():
                query += " AND p.sample_type = ?"
                params.append(self.sample_type_var.get())
            
            # فلتر الجنس
            if self.gender_var.get():
                query += " AND p.gender = ?"
                params.append(self.gender_var.get())
            
            # فلتر جهة الإرسال
            if self.sender_org_var.get():
                query += " AND p.sender_organization = ?"
                params.append(self.sender_org_var.get())
            
            query += " GROUP BY p.id ORDER BY p.national_id DESC"
            
            # تنفيذ الاستعلام
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, params)
            
            results = cursor.fetchall()
            conn.close()
            
            # عرض النتائج
            self.display_results(results)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
    
    def display_results(self, results):
        """عرض نتائج البحث"""
        # مسح النتائج الحالية
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # إضافة النتائج الجديدة
        for row in results:
            # تنسيق البيانات
            gender_text = "ذكر" if row[3] == 'M' else "أنثى"
            tests = row[7] if row[7] else "لا توجد تحاليل"
            results_text = row[8] if row[8] else "لا توجد نتائج"
            
            display_row = (
                row[0], row[1], row[2], gender_text, row[4], 
                row[5], row[6], tests, results_text
            )
            
            self.results_tree.insert('', 'end', values=display_row)
        
        # تحديث معلومات النتائج
        self.results_info_label.config(text=f"عدد النتائج: {len(results)}")
    
    def search_by_barcode(self):
        """البحث بالباركود (الرقم الوطني)"""
        barcode = self.barcode_var.get().strip()
        
        if not barcode:
            messagebox.showwarning("تحذير", "يرجى إدخال الباركود")
            return
        
        try:
            national_id = int(barcode)
            
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT p.national_id, p.name, p.age, p.gender, p.sample_type, 
                       p.sender_organization, p.sample_collection_date,
                       GROUP_CONCAT(DISTINCT pt.test_name) as tests,
                       GROUP_CONCAT(DISTINCT tr.result) as results
                FROM patients p
                LEFT JOIN patient_tests pt ON p.id = pt.patient_id
                LEFT JOIN test_results tr ON (p.id = tr.patient_id AND pt.test_name = tr.test_name)
                WHERE p.national_id = ?
                GROUP BY p.id
            ''', (national_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                self.display_results([result])
            else:
                messagebox.showinfo("لا توجد نتائج", "لم يتم العثور على مريض بهذا الباركود")
                self.display_results([])
                
        except ValueError:
            messagebox.showerror("خطأ", "الباركود يجب أن يكون رقماً")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
    
    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.start_date_var.set((datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"))
        self.end_date_var.set(datetime.now().strftime("%Y-%m-%d"))
        self.name_var.set("")
        self.sample_type_var.set("")
        self.gender_var.set("")
        self.sender_org_var.set("")
        self.barcode_var.set("")
        
        # مسح النتائج
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        self.results_info_label.config(text="عدد النتائج: 0")
    
    def on_double_click(self, event):
        """معالج النقر المزدوج لعرض تفاصيل المريض"""
        selection = self.results_tree.selection()
        if selection:
            item = self.results_tree.item(selection[0])
            national_id = item['values'][0]
            self.show_patient_details(national_id)
    
    def show_patient_details(self, national_id):
        """عرض تفاصيل المريض في نافذة منفصلة"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # الحصول على تفاصيل المريض
            cursor.execute('''
                SELECT * FROM patients WHERE national_id = ?
            ''', (national_id,))
            
            patient = cursor.fetchone()
            
            if patient:
                # إنشاء نافذة التفاصيل
                details_window = tk.Toplevel(self.main_frame)
                details_window.title(f"تفاصيل المريض - {patient[2]}")
                details_window.geometry("600x400")
                details_window.configure(bg=self.colors['white'])
                
                # عرض التفاصيل
                self.create_patient_details_content(details_window, patient, cursor)
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء عرض التفاصيل: {str(e)}")
    
    def create_patient_details_content(self, window, patient, cursor):
        """إنشاء محتوى نافذة تفاصيل المريض"""
        # معلومات المريض
        info_frame = tk.LabelFrame(window, text="معلومات المريض", font=self.font, bg=self.colors['white'])
        info_frame.pack(fill='x', padx=20, pady=10)
        
        details = [
            ("الرقم الوطني:", patient[1]),
            ("الاسم:", patient[2]),
            ("العمر:", patient[3]),
            ("الجنس:", "ذكر" if patient[4] == 'M' else "أنثى"),
            ("العنوان:", patient[5]),
            ("رقم الهاتف:", patient[6]),
            ("رقم الجواز:", patient[7] or "غير محدد"),
            ("رقم الوصل:", patient[8] or "غير محدد"),
            ("نوع العينة:", patient[9]),
            ("جهة الإرسال:", patient[10]),
            ("تاريخ سحب العينة:", patient[11]),
            ("تاريخ استلام العينة:", patient[12])
        ]
        
        for i, (label, value) in enumerate(details):
            row = i // 2
            col = (i % 2) * 2
            
            tk.Label(info_frame, text=label, font=self.font, bg=self.colors['white']).grid(
                row=row, column=col, sticky='w', padx=5, pady=2
            )
            tk.Label(info_frame, text=str(value), font=self.font, bg=self.colors['white']).grid(
                row=row, column=col+1, sticky='w', padx=5, pady=2
            )
        
        # التحاليل والنتائج
        tests_frame = tk.LabelFrame(window, text="التحاليل والنتائج", font=self.font, bg=self.colors['white'])
        tests_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # جدول التحاليل
        test_columns = ('التحليل', 'النتيجة', 'تاريخ النتيجة')
        test_tree = ttk.Treeview(tests_frame, columns=test_columns, show='headings', height=8)
        
        for col in test_columns:
            test_tree.heading(col, text=col)
            test_tree.column(col, width=150, anchor='center')
        
        # الحصول على التحاليل والنتائج
        cursor.execute('''
            SELECT pt.test_name, tr.result, tr.created_at
            FROM patient_tests pt
            LEFT JOIN test_results tr ON (pt.patient_id = tr.patient_id AND pt.test_name = tr.test_name)
            WHERE pt.patient_id = ?
        ''', (patient[0],))
        
        for row in cursor.fetchall():
            result = row[1] if row[1] else "لم يتم إدخال النتيجة"
            date = row[2] if row[2] else ""
            test_tree.insert('', 'end', values=(row[0], result, date))
        
        test_tree.pack(fill='both', expand=True, padx=10, pady=10)
        
        # زر طباعة التقرير الفردي
        print_btn = tk.Button(
            window,
            text="طباعة التقرير",
            command=lambda: self.print_individual_report(patient[1]),
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        print_btn.pack(pady=10)

    def generate_individual_report(self):
        """إنشاء تقرير فردي للمريض المختار"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض من النتائج")
            return

        item = self.results_tree.item(selection[0])
        national_id = item['values'][0]

        self.print_individual_report(national_id)

    def print_individual_report(self, national_id):
        """طباعة تقرير فردي"""
        try:
            # إنشاء التقرير
            report_path = self.report_generator.generate_individual_report(national_id)

            if report_path:
                # عرض التقرير قبل الطباعة
                if messagebox.askyesno("طباعة التقرير", "تم إنشاء التقرير بنجاح. هل تريد فتحه للمراجعة قبل الطباعة؟"):
                    import os
                    os.startfile(report_path)

                messagebox.showinfo("نجح", f"تم إنشاء التقرير بنجاح في:\n{report_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")

    def generate_group_report(self):
        """إنشاء تقرير جماعي للنتائج المعروضة"""
        # الحصول على جميع النتائج المعروضة
        displayed_results = []
        for item in self.results_tree.get_children():
            values = self.results_tree.item(item)['values']
            displayed_results.append(values[0])  # الرقم الوطني

        if not displayed_results:
            messagebox.showwarning("تحذير", "لا توجد نتائج لإنشاء التقرير")
            return

        try:
            # إنشاء التقرير الجماعي
            report_path = self.report_generator.generate_group_report(
                displayed_results,
                self.start_date_var.get(),
                self.end_date_var.get()
            )

            if report_path:
                # عرض التقرير قبل الطباعة
                if messagebox.askyesno("طباعة التقرير", "تم إنشاء التقرير بنجاح. هل تريد فتحه للمراجعة قبل الطباعة؟"):
                    import os
                    os.startfile(report_path)

                messagebox.showinfo("نجح", f"تم إنشاء التقرير الجماعي بنجاح في:\n{report_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء التقرير الجماعي: {str(e)}")

    def show_statistics(self):
        """عرض الإحصائيات"""
        try:
            # إنشاء نافذة الإحصائيات
            stats_window = tk.Toplevel(self.main_frame)
            stats_window.title("الإحصائيات")
            stats_window.geometry("800x600")
            stats_window.configure(bg=self.colors['white'])

            # عنوان النافذة
            title_label = tk.Label(
                stats_window,
                text="إحصائيات المختبر",
                font=('Arial', 16, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['primary']
            )
            title_label.pack(pady=10)

            # إطار الإحصائيات العامة
            general_stats_frame = tk.LabelFrame(
                stats_window,
                text="الإحصائيات العامة",
                font=self.font,
                bg=self.colors['white']
            )
            general_stats_frame.pack(fill='x', padx=20, pady=10)

            # حساب الإحصائيات
            stats = self.calculate_statistics()

            # عرض الإحصائيات العامة
            general_stats = [
                ("إجمالي المرضى:", stats['total_patients']),
                ("إجمالي العينات:", stats['total_samples']),
                ("إجمالي التحاليل:", stats['total_tests']),
                ("النتائج الإيجابية:", stats['positive_results']),
                ("النتائج السلبية:", stats['negative_results']),
                ("التحاليل المعادة:", stats['retest_results'])
            ]

            for i, (label, value) in enumerate(general_stats):
                row = i // 3
                col = (i % 3) * 2

                tk.Label(general_stats_frame, text=label, font=self.font, bg=self.colors['white']).grid(
                    row=row, column=col, sticky='w', padx=10, pady=5
                )
                tk.Label(general_stats_frame, text=str(value), font=('Arial', 10, 'bold'),
                        bg=self.colors['white'], fg=self.colors['primary']).grid(
                    row=row, column=col+1, sticky='w', padx=10, pady=5
                )

            # إطار إحصائيات حسب نوع العينة
            sample_stats_frame = tk.LabelFrame(
                stats_window,
                text="إحصائيات حسب نوع العينة",
                font=self.font,
                bg=self.colors['white']
            )
            sample_stats_frame.pack(fill='x', padx=20, pady=10)

            # جدول إحصائيات العينات
            sample_columns = ('نوع العينة', 'العدد', 'النسبة المئوية')
            sample_tree = ttk.Treeview(sample_stats_frame, columns=sample_columns, show='headings', height=6)

            for col in sample_columns:
                sample_tree.heading(col, text=col)
                sample_tree.column(col, width=150, anchor='center')

            for sample_type, count in stats['samples_by_type'].items():
                percentage = (count / stats['total_samples'] * 100) if stats['total_samples'] > 0 else 0
                sample_tree.insert('', 'end', values=(sample_type, count, f"{percentage:.1f}%"))

            sample_tree.pack(fill='x', padx=10, pady=10)

            # إطار إحصائيات حسب جهة الإرسال
            org_stats_frame = tk.LabelFrame(
                stats_window,
                text="إحصائيات حسب جهة الإرسال",
                font=self.font,
                bg=self.colors['white']
            )
            org_stats_frame.pack(fill='both', expand=True, padx=20, pady=10)

            # جدول إحصائيات جهات الإرسال
            org_columns = ('جهة الإرسال', 'العدد', 'النسبة المئوية')
            org_tree = ttk.Treeview(org_stats_frame, columns=org_columns, show='headings', height=8)

            for col in org_columns:
                org_tree.heading(col, text=col)
                org_tree.column(col, width=200, anchor='center')

            for org, count in stats['samples_by_org'].items():
                percentage = (count / stats['total_samples'] * 100) if stats['total_samples'] > 0 else 0
                org_tree.insert('', 'end', values=(org, count, f"{percentage:.1f}%"))

            org_tree.pack(fill='both', expand=True, padx=10, pady=10)

            # زر طباعة الإحصائيات
            print_stats_btn = tk.Button(
                stats_window,
                text="طباعة الإحصائيات",
                command=lambda: self.print_statistics(stats),
                font=self.font,
                bg=self.colors['primary'],
                fg=self.colors['white'],
                relief='raised',
                bd=3
            )
            print_stats_btn.pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء عرض الإحصائيات: {str(e)}")

    def calculate_statistics(self):
        """حساب الإحصائيات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            stats = {}

            # إجمالي المرضى
            cursor.execute('SELECT COUNT(*) FROM patients')
            stats['total_patients'] = cursor.fetchone()[0]

            # إجمالي العينات (نفس عدد المرضى)
            stats['total_samples'] = stats['total_patients']

            # إجمالي التحاليل
            cursor.execute('SELECT COUNT(*) FROM patient_tests')
            stats['total_tests'] = cursor.fetchone()[0]

            # النتائج حسب النوع
            cursor.execute('SELECT result, COUNT(*) FROM test_results GROUP BY result')
            result_counts = dict(cursor.fetchall())

            stats['positive_results'] = result_counts.get('Positive', 0)
            stats['negative_results'] = result_counts.get('Negative', 0)
            stats['retest_results'] = result_counts.get('Retest', 0)

            # إحصائيات حسب نوع العينة
            cursor.execute('SELECT sample_type, COUNT(*) FROM patients GROUP BY sample_type')
            stats['samples_by_type'] = dict(cursor.fetchall())

            # إحصائيات حسب جهة الإرسال
            cursor.execute('SELECT sender_organization, COUNT(*) FROM patients GROUP BY sender_organization')
            stats['samples_by_org'] = dict(cursor.fetchall())

            conn.close()
            return stats

        except Exception as e:
            raise Exception(f"خطأ في حساب الإحصائيات: {str(e)}")

    def print_statistics(self, stats):
        """طباعة الإحصائيات"""
        try:
            report_path = self.report_generator.generate_statistics_report(stats)

            if report_path:
                if messagebox.askyesno("طباعة الإحصائيات", "تم إنشاء تقرير الإحصائيات بنجاح. هل تريد فتحه للمراجعة؟"):
                    import os
                    os.startfile(report_path)

                messagebox.showinfo("نجح", f"تم إنشاء تقرير الإحصائيات بنجاح في:\n{report_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء طباعة الإحصائيات: {str(e)}")

    def refresh_data(self):
        """تحديث البيانات"""
        self.refresh_filter_options()

    def refresh_filter_options(self):
        """تحديث خيارات الفلاتر"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # تحديث أنواع العينات
            cursor.execute('SELECT DISTINCT sample_type FROM patients ORDER BY sample_type')
            sample_types = [''] + [row[0] for row in cursor.fetchall()]
            self.sample_type_combo['values'] = sample_types

            # تحديث جهات الإرسال
            cursor.execute('SELECT DISTINCT sender_organization FROM patients ORDER BY sender_organization')
            sender_orgs = [''] + [row[0] for row in cursor.fetchall()]
            self.sender_org_combo['values'] = sender_orgs

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث خيارات الفلاتر: {str(e)}")

    def show(self):
        """إظهار التبويب"""
        self.main_frame.pack(fill='both', expand=True)

    def hide(self):
        """إخفاء التبويب"""
        self.main_frame.pack_forget()
