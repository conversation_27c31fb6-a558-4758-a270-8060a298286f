#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج مختبر الصحة العامة المركزي - ذي قار
نظام إدارة شامل للمختبر يشمل إدخال البيانات، إدارة الوجبات، النتائج، التقارير والإعدادات

المطور: Augment Agent
التاريخ: 2024
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from main_window import MainWindow
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة في نفس المجلد")
    sys.exit(1)

def check_dependencies():
    """التحقق من وجود المكتبات المطلوبة"""
    missing_packages = []
    
    try:
        import sqlite3
    except ImportError:
        missing_packages.append("sqlite3")
    
    try:
        import openpyxl
    except ImportError:
        missing_packages.append("openpyxl")
    
    try:
        import reportlab
    except ImportError:
        missing_packages.append("reportlab")
    
    try:
        from PIL import Image
    except ImportError:
        missing_packages.append("Pillow")
    
    try:
        import win32print
    except ImportError:
        missing_packages.append("pywin32")
    
    if missing_packages:
        error_msg = "المكتبات التالية مفقودة:\n\n"
        for package in missing_packages:
            error_msg += f"- {package}\n"
        error_msg += "\nيرجى تثبيتها باستخدام الأمر:\n"
        error_msg += "pip install " + " ".join(missing_packages)
        
        messagebox.showerror("مكتبات مفقودة", error_msg)
        return False
    
    return True

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "مختبر الصحة العامة المركزي.lnk")
        target = os.path.abspath(__file__)
        wDir = os.path.dirname(target)
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        return True
    except:
        return False

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    # التحقق من المكتبات المطلوبة
    if not check_dependencies():
        return
    
    try:
        # إنشاء وتشغيل التطبيق
        app = MainWindow()
        
        # إنشاء اختصار على سطح المكتب (اختياري)
        try:
            create_desktop_shortcut()
        except:
            pass  # تجاهل الأخطاء في إنشاء الاختصار
        
        # تشغيل التطبيق
        app.run()
        
    except Exception as e:
        error_msg = f"حدث خطأ أثناء تشغيل التطبيق:\n\n{str(e)}"
        messagebox.showerror("خطأ في التطبيق", error_msg)
        print(f"خطأ: {e}")

if __name__ == "__main__":
    main()
