#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج مختبر الصحة العامة المركزي - ذي قار
نظام إدارة شامل للمختبر يشمل إدخال البيانات، إدارة الوجبات، النتائج، التقارير والإعدادات

المطور: Augment Agent
التاريخ: 2024
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from main_window import MainWindow
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة في نفس المجلد")
    sys.exit(1)

def check_dependencies():
    """التحقق من وجود المكتبات المطلوبة"""
    missing_packages = []
    
    try:
        import sqlite3
    except ImportError:
        missing_packages.append("sqlite3")
    
    try:
        import openpyxl
    except ImportError:
        missing_packages.append("openpyxl")
    
    try:
        import reportlab
    except ImportError:
        missing_packages.append("reportlab")
    
    try:
        from PIL import Image
    except ImportError:
        missing_packages.append("Pillow")
    
    try:
        import win32print
    except ImportError:
        missing_packages.append("pywin32")

    # فحص المكتبات العربية (اختيارية)
    arabic_packages = []
    try:
        import arabic_reshaper
    except ImportError:
        arabic_packages.append("arabic-reshaper")

    try:
        import bidi
    except ImportError:
        arabic_packages.append("python-bidi")

    if arabic_packages:
        print("⚠️ المكتبات العربية التالية مفقودة (اختيارية):")
        for package in arabic_packages:
            print(f"   - {package}")
        print("💡 لتحسين دعم النصوص العربية، يمكنك تثبيتها باستخدام:")
        print(f"   pip install {' '.join(arabic_packages)}")

    if missing_packages:
        error_msg = "المكتبات التالية مفقودة:\n\n"
        for package in missing_packages:
            error_msg += f"- {package}\n"
        error_msg += "\nيرجى تثبيتها باستخدام الأمر:\n"
        error_msg += "pip install " + " ".join(missing_packages)
        
        messagebox.showerror("مكتبات مفقودة", error_msg)
        return False
    
    return True

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "مختبر الصحة العامة المركزي.lnk")
        target = os.path.abspath(__file__)
        wDir = os.path.dirname(target)
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        return True
    except:
        return False

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    print("🚀 بدء تشغيل نظام إدارة المختبر الطبي...")

    # التحقق من المكتبات المطلوبة
    print("📦 التحقق من المكتبات المطلوبة...")
    if not check_dependencies():
        print("❌ فشل في التحقق من المكتبات")
        return
    print("✅ تم التحقق من جميع المكتبات بنجاح")

    try:
        print("🏗️ إنشاء النافذة الرئيسية...")
        # إنشاء وتشغيل التطبيق
        app = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")

        # إنشاء اختصار على سطح المكتب (اختياري)
        try:
            print("🔗 محاولة إنشاء اختصار على سطح المكتب...")
            create_desktop_shortcut()
            print("✅ تم إنشاء الاختصار بنجاح")
        except Exception as shortcut_error:
            print(f"⚠️ لم يتم إنشاء الاختصار: {shortcut_error}")

        # تشغيل التطبيق
        print("🎯 تشغيل التطبيق...")
        print("=" * 50)
        print("🎉 نظام إدارة المختبر الطبي جاهز للاستخدام!")
        print("=" * 50)
        app.run()

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        error_msg = f"حدث خطأ أثناء تشغيل التطبيق:\n\n{str(e)}\n\nتفاصيل الخطأ:\n{error_details}"
        print(f"❌ خطأ: {e}")
        print(f"📋 تفاصيل الخطأ:\n{error_details}")

        # إظهار رسالة خطأ للمستخدم
        try:
            messagebox.showerror("خطأ في التطبيق", error_msg)
        except:
            print("❌ فشل في إظهار رسالة الخطأ")

if __name__ == "__main__":
    main()
