#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تثبيت المتطلبات اللازمة لنظام إدارة المختبر
"""

import subprocess
import sys
import os

def install_package(package):
    """تثبيت حزمة Python"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """تثبيت جميع المتطلبات"""
    print("=" * 60)
    print("📦 تثبيت متطلبات نظام إدارة المختبر الطبي")
    print("=" * 60)
    
    # قائمة المتطلبات الأساسية
    requirements = [
        "openpyxl==3.1.2",
        "reportlab==4.0.4", 
        "Pillow==10.0.0",
        "pywin32==306",
        "python-bidi==0.6.6",
        "arabic-reshaper==3.0.0"
    ]
    
    # المتطلبات الاختيارية
    optional_requirements = [
        "winshell==0.6"
    ]
    
    print("🔧 تثبيت المتطلبات الأساسية...")
    failed_packages = []
    
    for package in requirements:
        print(f"📦 تثبيت {package}...")
        if install_package(package):
            print(f"✅ تم تثبيت {package} بنجاح")
        else:
            print(f"❌ فشل تثبيت {package}")
            failed_packages.append(package)
    
    print("\n🔧 تثبيت المتطلبات الاختيارية...")
    for package in optional_requirements:
        print(f"📦 تثبيت {package}...")
        if install_package(package):
            print(f"✅ تم تثبيت {package} بنجاح")
        else:
            print(f"⚠️ لم يتم تثبيت {package} (اختياري)")
    
    print("\n" + "=" * 60)
    if failed_packages:
        print("❌ فشل تثبيت بعض المتطلبات:")
        for package in failed_packages:
            print(f"   - {package}")
        print("\n💡 يرجى تثبيتها يدوياً باستخدام:")
        print("   pip install " + " ".join(failed_packages))
    else:
        print("🎉 تم تثبيت جميع المتطلبات بنجاح!")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
