import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3

class SettingsTab:
    def __init__(self, parent, db, colors, font):
        self.parent = parent
        self.db = db
        self.colors = colors
        self.font = font
        
        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء عناصر واجهة تبويب الإعدادات"""
        # الإطار الرئيسي
        self.main_frame = tk.Frame(self.parent, bg=self.colors['white'])
        
        # عنوان التبويب
        title_label = tk.Label(
            self.main_frame,
            text="إعدادات النظام",
            font=('Arial', 14, 'bold'),
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        title_label.pack(pady=10)
        
        # إنشاء دفتر التبويبات الفرعية
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill='both', expand=True, padx=20, pady=10)
        
        # تبويبات الإعدادات
        self.create_sample_types_tab()
        self.create_sender_orgs_tab()
        self.create_tests_tab()
        self.create_technicians_tab()
        self.create_printer_settings_tab()
        self.create_report_settings_tab()

        # تحديث البيانات
        self.refresh_data()
        
    def create_sample_types_tab(self):
        """إنشاء تبويب أنواع العينات"""
        sample_types_frame = ttk.Frame(self.notebook)
        self.notebook.add(sample_types_frame, text="أنواع العينات")
        
        # إطار الإضافة
        add_frame = tk.LabelFrame(sample_types_frame, text="إضافة نوع عينة جديد", font=self.font)
        add_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(add_frame, text="اسم نوع العينة:", font=self.font).pack(side='left', padx=5)
        self.sample_type_var = tk.StringVar()
        sample_type_entry = tk.Entry(add_frame, textvariable=self.sample_type_var, font=self.font, width=20)
        sample_type_entry.pack(side='left', padx=5)
        
        add_sample_btn = tk.Button(
            add_frame,
            text="إضافة",
            command=self.add_sample_type,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        add_sample_btn.pack(side='left', padx=5)
        
        # إطار القائمة
        list_frame = tk.LabelFrame(sample_types_frame, text="أنواع العينات الموجودة", font=self.font)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # جدول أنواع العينات
        columns = ('ID', 'نوع العينة', 'تاريخ الإضافة')
        self.sample_types_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.sample_types_tree.heading(col, text=col)
            if col == 'ID':
                self.sample_types_tree.column(col, width=50, anchor='center')
            else:
                self.sample_types_tree.column(col, width=150, anchor='center')
        
        # شريط التمرير
        sample_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.sample_types_tree.yview)
        self.sample_types_tree.configure(yscrollcommand=sample_scrollbar.set)
        
        self.sample_types_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        sample_scrollbar.pack(side='right', fill='y', pady=10)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(list_frame)
        buttons_frame.pack(fill='x', padx=10, pady=5)
        
        edit_sample_btn = tk.Button(
            buttons_frame,
            text="تعديل",
            command=self.edit_sample_type,
            font=self.font,
            bg=self.colors['background'],
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        edit_sample_btn.pack(side='left', padx=5)
        
        delete_sample_btn = tk.Button(
            buttons_frame,
            text="حذف",
            command=self.delete_sample_type,
            font=self.font,
            bg='#DC3545',
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        delete_sample_btn.pack(side='left', padx=5)
        
    def create_sender_orgs_tab(self):
        """إنشاء تبويب جهات الإرسال"""
        sender_orgs_frame = ttk.Frame(self.notebook)
        self.notebook.add(sender_orgs_frame, text="جهات الإرسال")
        
        # إطار الإضافة
        add_frame = tk.LabelFrame(sender_orgs_frame, text="إضافة جهة إرسال جديدة", font=self.font)
        add_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(add_frame, text="اسم جهة الإرسال:", font=self.font).pack(side='left', padx=5)
        self.sender_org_var = tk.StringVar()
        sender_org_entry = tk.Entry(add_frame, textvariable=self.sender_org_var, font=self.font, width=30)
        sender_org_entry.pack(side='left', padx=5)
        
        add_org_btn = tk.Button(
            add_frame,
            text="إضافة",
            command=self.add_sender_org,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        add_org_btn.pack(side='left', padx=5)
        
        # إطار القائمة
        list_frame = tk.LabelFrame(sender_orgs_frame, text="جهات الإرسال الموجودة", font=self.font)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # جدول جهات الإرسال
        columns = ('ID', 'جهة الإرسال', 'تاريخ الإضافة')
        self.sender_orgs_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.sender_orgs_tree.heading(col, text=col)
            if col == 'ID':
                self.sender_orgs_tree.column(col, width=50, anchor='center')
            else:
                self.sender_orgs_tree.column(col, width=200, anchor='center')
        
        # شريط التمرير
        org_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.sender_orgs_tree.yview)
        self.sender_orgs_tree.configure(yscrollcommand=org_scrollbar.set)
        
        self.sender_orgs_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        org_scrollbar.pack(side='right', fill='y', pady=10)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(list_frame)
        buttons_frame.pack(fill='x', padx=10, pady=5)
        
        edit_org_btn = tk.Button(
            buttons_frame,
            text="تعديل",
            command=self.edit_sender_org,
            font=self.font,
            bg=self.colors['background'],
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        edit_org_btn.pack(side='left', padx=5)
        
        delete_org_btn = tk.Button(
            buttons_frame,
            text="حذف",
            command=self.delete_sender_org,
            font=self.font,
            bg='#DC3545',
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        delete_org_btn.pack(side='left', padx=5)
        
    def create_tests_tab(self):
        """إنشاء تبويب التحاليل"""
        tests_frame = ttk.Frame(self.notebook)
        self.notebook.add(tests_frame, text="التحاليل")
        
        # إطار الإضافة
        add_frame = tk.LabelFrame(tests_frame, text="إضافة تحليل جديد", font=self.font)
        add_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(add_frame, text="اسم التحليل:", font=self.font).pack(side='left', padx=5)
        self.test_var = tk.StringVar()
        test_entry = tk.Entry(add_frame, textvariable=self.test_var, font=self.font, width=30)
        test_entry.pack(side='left', padx=5)
        
        add_test_btn = tk.Button(
            add_frame,
            text="إضافة",
            command=self.add_test,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        add_test_btn.pack(side='left', padx=5)
        
        # إطار القائمة
        list_frame = tk.LabelFrame(tests_frame, text="التحاليل الموجودة", font=self.font)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # جدول التحاليل
        columns = ('ID', 'اسم التحليل', 'تاريخ الإضافة')
        self.tests_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.tests_tree.heading(col, text=col)
            if col == 'ID':
                self.tests_tree.column(col, width=50, anchor='center')
            else:
                self.tests_tree.column(col, width=200, anchor='center')
        
        # شريط التمرير
        test_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.tests_tree.yview)
        self.tests_tree.configure(yscrollcommand=test_scrollbar.set)
        
        self.tests_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        test_scrollbar.pack(side='right', fill='y', pady=10)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(list_frame)
        buttons_frame.pack(fill='x', padx=10, pady=5)
        
        edit_test_btn = tk.Button(
            buttons_frame,
            text="تعديل",
            command=self.edit_test,
            font=self.font,
            bg=self.colors['background'],
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        edit_test_btn.pack(side='left', padx=5)
        
        delete_test_btn = tk.Button(
            buttons_frame,
            text="حذف",
            command=self.delete_test,
            font=self.font,
            bg='#DC3545',
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        delete_test_btn.pack(side='left', padx=5)
        
    def create_technicians_tab(self):
        """إنشاء تبويب الفنيين"""
        technicians_frame = ttk.Frame(self.notebook)
        self.notebook.add(technicians_frame, text="الفنيين")
        
        # إطار الإضافة
        add_frame = tk.LabelFrame(technicians_frame, text="إضافة فني جديد", font=self.font)
        add_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(add_frame, text="اسم الفني:", font=self.font).pack(side='left', padx=5)
        self.technician_var = tk.StringVar()
        technician_entry = tk.Entry(add_frame, textvariable=self.technician_var, font=self.font, width=25)
        technician_entry.pack(side='left', padx=5)
        
        add_tech_btn = tk.Button(
            add_frame,
            text="إضافة",
            command=self.add_technician,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        add_tech_btn.pack(side='left', padx=5)
        
        # إطار القائمة
        list_frame = tk.LabelFrame(technicians_frame, text="الفنيين الموجودين", font=self.font)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # جدول الفنيين
        columns = ('ID', 'اسم الفني', 'تاريخ الإضافة')
        self.technicians_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.technicians_tree.heading(col, text=col)
            if col == 'ID':
                self.technicians_tree.column(col, width=50, anchor='center')
            else:
                self.technicians_tree.column(col, width=200, anchor='center')
        
        # شريط التمرير
        tech_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.technicians_tree.yview)
        self.technicians_tree.configure(yscrollcommand=tech_scrollbar.set)
        
        self.technicians_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        tech_scrollbar.pack(side='right', fill='y', pady=10)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(list_frame)
        buttons_frame.pack(fill='x', padx=10, pady=5)
        
        edit_tech_btn = tk.Button(
            buttons_frame,
            text="تعديل",
            command=self.edit_technician,
            font=self.font,
            bg=self.colors['background'],
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        edit_tech_btn.pack(side='left', padx=5)
        
        delete_tech_btn = tk.Button(
            buttons_frame,
            text="حذف",
            command=self.delete_technician,
            font=self.font,
            bg='#DC3545',
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        delete_tech_btn.pack(side='left', padx=5)

    def create_printer_settings_tab(self):
        """إنشاء تبويب إعدادات الطابعة"""
        printer_frame = ttk.Frame(self.notebook)
        self.notebook.add(printer_frame, text="إعدادات الطابعة")

        # إطار اختيار الطابعة
        printer_selection_frame = tk.LabelFrame(printer_frame, text="اختيار الطابعة", font=self.font)
        printer_selection_frame.pack(fill='x', padx=10, pady=10)

        tk.Label(printer_selection_frame, text="الطابعة المحددة:", font=self.font).pack(side='left', padx=5)

        self.printer_var = tk.StringVar()
        self.printer_combo = ttk.Combobox(
            printer_selection_frame,
            textvariable=self.printer_var,
            font=self.font,
            width=30,
            state='readonly'
        )
        self.printer_combo.pack(side='left', padx=5)

        refresh_printers_btn = tk.Button(
            printer_selection_frame,
            text="تحديث قائمة الطابعات",
            command=self.refresh_printers,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white'],
            relief='raised',
            bd=3
        )
        refresh_printers_btn.pack(side='left', padx=5)

        # إطار اختبار الطابعة
        test_frame = tk.LabelFrame(printer_frame, text="اختبار الطابعة", font=self.font)
        test_frame.pack(fill='x', padx=10, pady=10)

        test_printer_btn = tk.Button(
            test_frame,
            text="اختبار طباعة ستيكر",
            command=self.test_printer,
            font=self.font,
            bg='#28A745',
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=20
        )
        test_printer_btn.pack(pady=10)

        # إطار معلومات الطابعة
        info_frame = tk.LabelFrame(printer_frame, text="معلومات الطابعة", font=self.font)
        info_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.printer_info_text = tk.Text(info_frame, height=10, width=50, font=self.font)
        self.printer_info_text.pack(fill='both', expand=True, padx=10, pady=10)

        # تحديث قائمة الطابعات عند الإنشاء
        self.refresh_printers()

    # وظائف أنواع العينات
    def add_sample_type(self):
        """إضافة نوع عينة جديد"""
        sample_type = self.sample_type_var.get().strip()

        if not sample_type:
            messagebox.showwarning("تحذير", "يرجى إدخال نوع العينة")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('INSERT INTO sample_types (name) VALUES (?)', (sample_type,))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة نوع العينة بنجاح")
            self.sample_type_var.set("")
            self.refresh_sample_types()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "نوع العينة موجود مسبقاً")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الإضافة: {str(e)}")

    def edit_sample_type(self):
        """تعديل نوع عينة"""
        selection = self.sample_types_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار نوع عينة للتعديل")
            return

        item = self.sample_types_tree.item(selection[0])
        sample_id = item['values'][0]
        current_name = item['values'][1]

        # نافذة التعديل
        edit_window = tk.Toplevel(self.main_frame)
        edit_window.title("تعديل نوع العينة")
        edit_window.geometry("300x150")
        edit_window.configure(bg=self.colors['white'])

        tk.Label(edit_window, text="اسم نوع العينة:", font=self.font, bg=self.colors['white']).pack(pady=10)

        new_name_var = tk.StringVar(value=current_name)
        new_name_entry = tk.Entry(edit_window, textvariable=new_name_var, font=self.font, width=25)
        new_name_entry.pack(pady=5)

        def save_changes():
            new_name = new_name_var.get().strip()
            if not new_name:
                messagebox.showwarning("تحذير", "يرجى إدخال اسم نوع العينة")
                return

            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                cursor.execute('UPDATE sample_types SET name = ? WHERE id = ?', (new_name, sample_id))
                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم تعديل نوع العينة بنجاح")
                edit_window.destroy()
                self.refresh_sample_types()

            except sqlite3.IntegrityError:
                messagebox.showerror("خطأ", "نوع العينة موجود مسبقاً")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء التعديل: {str(e)}")

        save_btn = tk.Button(
            edit_window,
            text="حفظ",
            command=save_changes,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white']
        )
        save_btn.pack(pady=10)

    def delete_sample_type(self):
        """حذف نوع عينة"""
        selection = self.sample_types_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار نوع عينة للحذف")
            return

        item = self.sample_types_tree.item(selection[0])
        sample_id = item['values'][0]
        sample_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف نوع العينة '{sample_name}'؟"):
            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # التحقق من وجود مرضى يستخدمون هذا النوع
                cursor.execute('SELECT COUNT(*) FROM patients WHERE sample_type = ?', (sample_name,))
                count = cursor.fetchone()[0]

                if count > 0:
                    messagebox.showwarning("تحذير", f"لا يمكن حذف نوع العينة لأنه مستخدم في {count} عينة")
                    conn.close()
                    return

                cursor.execute('DELETE FROM sample_types WHERE id = ?', (sample_id,))
                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم حذف نوع العينة بنجاح")
                self.refresh_sample_types()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")

    # وظائف جهات الإرسال
    def add_sender_org(self):
        """إضافة جهة إرسال جديدة"""
        org_name = self.sender_org_var.get().strip()

        if not org_name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم جهة الإرسال")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('INSERT INTO sender_organizations (name) VALUES (?)', (org_name,))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة جهة الإرسال بنجاح")
            self.sender_org_var.set("")
            self.refresh_sender_orgs()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "جهة الإرسال موجودة مسبقاً")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الإضافة: {str(e)}")

    def edit_sender_org(self):
        """تعديل جهة إرسال"""
        selection = self.sender_orgs_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار جهة إرسال للتعديل")
            return

        item = self.sender_orgs_tree.item(selection[0])
        org_id = item['values'][0]
        current_name = item['values'][1]

        # نافذة التعديل
        edit_window = tk.Toplevel(self.main_frame)
        edit_window.title("تعديل جهة الإرسال")
        edit_window.geometry("400x150")
        edit_window.configure(bg=self.colors['white'])

        tk.Label(edit_window, text="اسم جهة الإرسال:", font=self.font, bg=self.colors['white']).pack(pady=10)

        new_name_var = tk.StringVar(value=current_name)
        new_name_entry = tk.Entry(edit_window, textvariable=new_name_var, font=self.font, width=35)
        new_name_entry.pack(pady=5)

        def save_changes():
            new_name = new_name_var.get().strip()
            if not new_name:
                messagebox.showwarning("تحذير", "يرجى إدخال اسم جهة الإرسال")
                return

            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                cursor.execute('UPDATE sender_organizations SET name = ? WHERE id = ?', (new_name, org_id))

                # تحديث جهة الإرسال في جدول المرضى أيضاً
                cursor.execute('UPDATE patients SET sender_organization = ? WHERE sender_organization = ?',
                             (new_name, current_name))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم تعديل جهة الإرسال بنجاح")
                edit_window.destroy()
                self.refresh_sender_orgs()

            except sqlite3.IntegrityError:
                messagebox.showerror("خطأ", "جهة الإرسال موجودة مسبقاً")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء التعديل: {str(e)}")

        save_btn = tk.Button(
            edit_window,
            text="حفظ",
            command=save_changes,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white']
        )
        save_btn.pack(pady=10)

    def delete_sender_org(self):
        """حذف جهة إرسال"""
        selection = self.sender_orgs_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار جهة إرسال للحذف")
            return

        item = self.sender_orgs_tree.item(selection[0])
        org_id = item['values'][0]
        org_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف جهة الإرسال '{org_name}'؟"):
            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # التحقق من وجود مرضى من هذه الجهة
                cursor.execute('SELECT COUNT(*) FROM patients WHERE sender_organization = ?', (org_name,))
                count = cursor.fetchone()[0]

                if count > 0:
                    messagebox.showwarning("تحذير", f"لا يمكن حذف جهة الإرسال لأنها مستخدمة في {count} عينة")
                    conn.close()
                    return

                cursor.execute('DELETE FROM sender_organizations WHERE id = ?', (org_id,))
                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم حذف جهة الإرسال بنجاح")
                self.refresh_sender_orgs()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")

    # وظائف التحاليل
    def add_test(self):
        """إضافة تحليل جديد"""
        test_name = self.test_var.get().strip()

        if not test_name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم التحليل")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('INSERT INTO available_tests (name) VALUES (?)', (test_name,))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة التحليل بنجاح")
            self.test_var.set("")
            self.refresh_tests()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "التحليل موجود مسبقاً")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الإضافة: {str(e)}")

    def edit_test(self):
        """تعديل تحليل"""
        selection = self.tests_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل للتعديل")
            return

        item = self.tests_tree.item(selection[0])
        test_id = item['values'][0]
        current_name = item['values'][1]

        # نافذة التعديل
        edit_window = tk.Toplevel(self.main_frame)
        edit_window.title("تعديل التحليل")
        edit_window.geometry("400x150")
        edit_window.configure(bg=self.colors['white'])

        tk.Label(edit_window, text="اسم التحليل:", font=self.font, bg=self.colors['white']).pack(pady=10)

        new_name_var = tk.StringVar(value=current_name)
        new_name_entry = tk.Entry(edit_window, textvariable=new_name_var, font=self.font, width=35)
        new_name_entry.pack(pady=5)

        def save_changes():
            new_name = new_name_var.get().strip()
            if not new_name:
                messagebox.showwarning("تحذير", "يرجى إدخال اسم التحليل")
                return

            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                cursor.execute('UPDATE available_tests SET name = ? WHERE id = ?', (new_name, test_id))

                # تحديث اسم التحليل في الجداول الأخرى
                cursor.execute('UPDATE patient_tests SET test_name = ? WHERE test_name = ?',
                             (new_name, current_name))
                cursor.execute('UPDATE test_results SET test_name = ? WHERE test_name = ?',
                             (new_name, current_name))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم تعديل التحليل بنجاح")
                edit_window.destroy()
                self.refresh_tests()

            except sqlite3.IntegrityError:
                messagebox.showerror("خطأ", "التحليل موجود مسبقاً")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء التعديل: {str(e)}")

        save_btn = tk.Button(
            edit_window,
            text="حفظ",
            command=save_changes,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white']
        )
        save_btn.pack(pady=10)

    def delete_test(self):
        """حذف تحليل"""
        selection = self.tests_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل للحذف")
            return

        item = self.tests_tree.item(selection[0])
        test_id = item['values'][0]
        test_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف التحليل '{test_name}'؟"):
            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # التحقق من وجود تحاليل مرتبطة
                cursor.execute('SELECT COUNT(*) FROM patient_tests WHERE test_name = ?', (test_name,))
                count = cursor.fetchone()[0]

                if count > 0:
                    messagebox.showwarning("تحذير", f"لا يمكن حذف التحليل لأنه مستخدم في {count} حالة")
                    conn.close()
                    return

                cursor.execute('DELETE FROM available_tests WHERE id = ?', (test_id,))
                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم حذف التحليل بنجاح")
                self.refresh_tests()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")

    # وظائف الفنيين
    def add_technician(self):
        """إضافة فني جديد"""
        tech_name = self.technician_var.get().strip()

        if not tech_name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم الفني")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('INSERT INTO technicians (name) VALUES (?)', (tech_name,))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة الفني بنجاح")
            self.technician_var.set("")
            self.refresh_technicians()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "الفني موجود مسبقاً")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء الإضافة: {str(e)}")

    def edit_technician(self):
        """تعديل فني"""
        selection = self.technicians_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار فني للتعديل")
            return

        item = self.technicians_tree.item(selection[0])
        tech_id = item['values'][0]
        current_name = item['values'][1]

        # نافذة التعديل
        edit_window = tk.Toplevel(self.main_frame)
        edit_window.title("تعديل الفني")
        edit_window.geometry("300x150")
        edit_window.configure(bg=self.colors['white'])

        tk.Label(edit_window, text="اسم الفني:", font=self.font, bg=self.colors['white']).pack(pady=10)

        new_name_var = tk.StringVar(value=current_name)
        new_name_entry = tk.Entry(edit_window, textvariable=new_name_var, font=self.font, width=25)
        new_name_entry.pack(pady=5)

        def save_changes():
            new_name = new_name_var.get().strip()
            if not new_name:
                messagebox.showwarning("تحذير", "يرجى إدخال اسم الفني")
                return

            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                cursor.execute('UPDATE technicians SET name = ? WHERE id = ?', (new_name, tech_id))

                # تحديث اسم الفني في جدول الوجبات
                cursor.execute('UPDATE batch_samples SET technician_name = ? WHERE technician_name = ?',
                             (new_name, current_name))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم تعديل الفني بنجاح")
                edit_window.destroy()
                self.refresh_technicians()

            except sqlite3.IntegrityError:
                messagebox.showerror("خطأ", "الفني موجود مسبقاً")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء التعديل: {str(e)}")

        save_btn = tk.Button(
            edit_window,
            text="حفظ",
            command=save_changes,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white']
        )
        save_btn.pack(pady=10)

    def delete_technician(self):
        """حذف فني"""
        selection = self.technicians_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار فني للحذف")
            return

        item = self.technicians_tree.item(selection[0])
        tech_id = item['values'][0]
        tech_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف الفني '{tech_name}'؟"):
            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # التحقق من وجود وجبات مرتبطة
                cursor.execute('SELECT COUNT(*) FROM batch_samples WHERE technician_name = ?', (tech_name,))
                count = cursor.fetchone()[0]

                if count > 0:
                    if not messagebox.askyesno("تحذير", f"الفني مرتبط بـ {count} وجبة عمل. هل تريد المتابعة؟"):
                        conn.close()
                        return

                    # إزالة ارتباط الفني من الوجبات
                    cursor.execute('UPDATE batch_samples SET technician_name = NULL WHERE technician_name = ?', (tech_name,))

                cursor.execute('DELETE FROM technicians WHERE id = ?', (tech_id,))
                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم حذف الفني بنجاح")
                self.refresh_technicians()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")

    # وظائف إعدادات الطابعة
    def refresh_printers(self):
        """تحديث قائمة الطابعات"""
        try:
            from sticker_printer import StickerPrinter
            printer = StickerPrinter()

            available_printers = printer.get_available_printers()
            self.printer_combo['values'] = available_printers

            # تحديد الطابعة الافتراضية
            default_printer = printer.get_default_printer()
            if default_printer and default_printer in available_printers:
                self.printer_var.set(default_printer)

            # عرض معلومات الطابعات
            info_text = "الطابعات المتاحة:\n\n"
            for i, printer_name in enumerate(available_printers, 1):
                status = " (افتراضية)" if printer_name == default_printer else ""
                info_text += f"{i}. {printer_name}{status}\n"

            if not available_printers:
                info_text = "لم يتم العثور على طابعات متاحة.\n\nتأكد من:\n- تثبيت الطابعة بشكل صحيح\n- تشغيل الطابعة\n- اتصال الطابعة بالكمبيوتر"

            self.printer_info_text.delete(1.0, tk.END)
            self.printer_info_text.insert(1.0, info_text)

        except Exception as e:
            error_text = f"حدث خطأ أثناء البحث عن الطابعات:\n{str(e)}\n\nتأكد من تثبيت برامج تشغيل الطابعة."
            self.printer_info_text.delete(1.0, tk.END)
            self.printer_info_text.insert(1.0, error_text)

    def test_printer(self):
        """اختبار الطابعة"""
        selected_printer = self.printer_var.get()

        if not selected_printer:
            messagebox.showwarning("تحذير", "يرجى اختيار طابعة أولاً")
            return

        try:
            from sticker_printer import StickerPrinter
            printer = StickerPrinter()
            printer.set_printer(selected_printer)

            # اختبار الطابعة
            success, message = printer.test_printer()

            if success:
                messagebox.showinfo("نجح الاختبار", message)
            else:
                messagebox.showerror("فشل الاختبار", message)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء اختبار الطابعة: {str(e)}")

    # وظائف تحديث البيانات
    def refresh_sample_types(self):
        """تحديث قائمة أنواع العينات"""
        for item in self.sample_types_tree.get_children():
            self.sample_types_tree.delete(item)

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT id, name, created_at FROM sample_types ORDER BY name')
            for row in cursor.fetchall():
                self.sample_types_tree.insert('', 'end', values=row)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث أنواع العينات: {str(e)}")

    def refresh_sender_orgs(self):
        """تحديث قائمة جهات الإرسال"""
        for item in self.sender_orgs_tree.get_children():
            self.sender_orgs_tree.delete(item)

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT id, name, created_at FROM sender_organizations ORDER BY name')
            for row in cursor.fetchall():
                self.sender_orgs_tree.insert('', 'end', values=row)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث جهات الإرسال: {str(e)}")

    def refresh_tests(self):
        """تحديث قائمة التحاليل"""
        for item in self.tests_tree.get_children():
            self.tests_tree.delete(item)

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT id, name, created_at FROM available_tests ORDER BY name')
            for row in cursor.fetchall():
                self.tests_tree.insert('', 'end', values=row)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث التحاليل: {str(e)}")

    def refresh_technicians(self):
        """تحديث قائمة الفنيين"""
        for item in self.technicians_tree.get_children():
            self.technicians_tree.delete(item)

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT id, name, created_at FROM technicians ORDER BY name')
            for row in cursor.fetchall():
                self.technicians_tree.insert('', 'end', values=row)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث الفنيين: {str(e)}")

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.refresh_sample_types()
        self.refresh_sender_orgs()
        self.refresh_tests()
        self.refresh_technicians()
        self.refresh_printers()
        self.refresh_report_settings()

    def create_report_settings_tab(self):
        """إنشاء تبويب إعدادات التقرير"""
        report_frame = ttk.Frame(self.notebook)
        self.notebook.add(report_frame, text="إعدادات التقرير")

        # إطار إعدادات عامة
        general_frame = tk.LabelFrame(report_frame, text="الإعدادات العامة", font=self.font)
        general_frame.pack(fill='x', padx=10, pady=10)

        # اسم المختبر
        lab_name_frame = tk.Frame(general_frame)
        lab_name_frame.pack(fill='x', padx=5, pady=5)

        tk.Label(lab_name_frame, text="اسم المختبر:", font=self.font, width=15, anchor='e').pack(side='left')
        self.lab_name_var = tk.StringVar(value="مختبر الصحة العامة المركزي - ذي قار")
        lab_name_entry = tk.Entry(lab_name_frame, textvariable=self.lab_name_var, font=self.font, width=40)
        lab_name_entry.pack(side='left', padx=5)

        # اسم الوزارة
        ministry_frame = tk.Frame(general_frame)
        ministry_frame.pack(fill='x', padx=5, pady=5)

        tk.Label(ministry_frame, text="اسم الوزارة:", font=self.font, width=15, anchor='e').pack(side='left')
        self.ministry_name_var = tk.StringVar(value="وزارة الصحة العراقية")
        ministry_entry = tk.Entry(ministry_frame, textvariable=self.ministry_name_var, font=self.font, width=40)
        ministry_entry.pack(side='left', padx=5)

        # عنوان المختبر
        address_frame = tk.Frame(general_frame)
        address_frame.pack(fill='x', padx=5, pady=5)

        tk.Label(address_frame, text="عنوان المختبر:", font=self.font, width=15, anchor='e').pack(side='left')
        self.lab_address_var = tk.StringVar(value="محافظة ذي قار - العراق")
        address_entry = tk.Entry(address_frame, textvariable=self.lab_address_var, font=self.font, width=40)
        address_entry.pack(side='left', padx=5)

        # رقم هاتف المختبر
        phone_frame = tk.Frame(general_frame)
        phone_frame.pack(fill='x', padx=5, pady=5)

        tk.Label(phone_frame, text="رقم الهاتف:", font=self.font, width=15, anchor='e').pack(side='left')
        self.lab_phone_var = tk.StringVar(value="")
        phone_entry = tk.Entry(phone_frame, textvariable=self.lab_phone_var, font=self.font, width=40)
        phone_entry.pack(side='left', padx=5)

        # إطار إعدادات الخط
        font_frame = tk.LabelFrame(report_frame, text="إعدادات الخط", font=self.font)
        font_frame.pack(fill='x', padx=10, pady=10)

        # نوع الخط
        font_type_frame = tk.Frame(font_frame)
        font_type_frame.pack(fill='x', padx=5, pady=5)

        tk.Label(font_type_frame, text="نوع الخط:", font=self.font, width=15, anchor='e').pack(side='left')
        self.font_type_var = tk.StringVar(value="Tahoma")
        font_combo = ttk.Combobox(font_type_frame, textvariable=self.font_type_var,
                                 values=["Tahoma", "Arial", "Calibri", "Times New Roman"],
                                 state='readonly', width=20)
        font_combo.pack(side='left', padx=5)

        # حجم الخط
        font_size_frame = tk.Frame(font_frame)
        font_size_frame.pack(fill='x', padx=5, pady=5)

        tk.Label(font_size_frame, text="حجم الخط:", font=self.font, width=15, anchor='e').pack(side='left')
        self.font_size_var = tk.StringVar(value="12")
        font_size_combo = ttk.Combobox(font_size_frame, textvariable=self.font_size_var,
                                      values=["8", "9", "10", "11", "12", "14", "16", "18"],
                                      state='readonly', width=10)
        font_size_combo.pack(side='left', padx=5)

        # إطار إعدادات التخطيط
        layout_frame = tk.LabelFrame(report_frame, text="إعدادات التخطيط", font=self.font)
        layout_frame.pack(fill='x', padx=10, pady=10)

        # إظهار الشعار
        self.show_logo_var = tk.BooleanVar(value=True)
        logo_check = tk.Checkbutton(layout_frame, text="إظهار شعار الوزارة",
                                   variable=self.show_logo_var, font=self.font)
        logo_check.pack(anchor='w', padx=5, pady=2)

        # إظهار التاريخ
        self.show_date_var = tk.BooleanVar(value=True)
        date_check = tk.Checkbutton(layout_frame, text="إظهار تاريخ إصدار التقرير",
                                   variable=self.show_date_var, font=self.font)
        date_check.pack(anchor='w', padx=5, pady=2)

        # إظهار التوقيع
        self.show_signature_var = tk.BooleanVar(value=True)
        signature_check = tk.Checkbutton(layout_frame, text="إظهار توقيع المختبر",
                                        variable=self.show_signature_var, font=self.font)
        signature_check.pack(anchor='w', padx=5, pady=2)

        # أزرار الحفظ والاستعادة
        buttons_frame = tk.Frame(report_frame)
        buttons_frame.pack(fill='x', padx=10, pady=20)

        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ الإعدادات",
            command=self.save_report_settings,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=15
        )
        save_btn.pack(side='left', padx=5)

        reset_btn = tk.Button(
            buttons_frame,
            text="🔄 استعادة الافتراضي",
            command=self.reset_report_settings,
            font=self.font,
            bg=self.colors['secondary'],
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=15
        )
        reset_btn.pack(side='left', padx=5)

        test_btn = tk.Button(
            buttons_frame,
            text="🧪 اختبار التقرير",
            command=self.test_report_settings,
            font=self.font,
            bg='#17A2B8',
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=15
        )
        test_btn.pack(side='left', padx=5)

    def save_report_settings(self):
        """حفظ إعدادات التقرير"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # إنشاء جدول الإعدادات إذا لم يكن موجوداً
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS report_settings (
                    id INTEGER PRIMARY KEY,
                    lab_name TEXT,
                    ministry_name TEXT,
                    lab_address TEXT,
                    lab_phone TEXT,
                    font_type TEXT,
                    font_size INTEGER,
                    show_logo BOOLEAN,
                    show_date BOOLEAN,
                    show_signature BOOLEAN
                )
            ''')

            # حذف الإعدادات السابقة
            cursor.execute('DELETE FROM report_settings')

            # إدراج الإعدادات الجديدة
            cursor.execute('''
                INSERT INTO report_settings
                (lab_name, ministry_name, lab_address, lab_phone, font_type, font_size,
                 show_logo, show_date, show_signature)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.lab_name_var.get(),
                self.ministry_name_var.get(),
                self.lab_address_var.get(),
                self.lab_phone_var.get(),
                self.font_type_var.get(),
                int(self.font_size_var.get()),
                self.show_logo_var.get(),
                self.show_date_var.get(),
                self.show_signature_var.get()
            ))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم حفظ إعدادات التقرير بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}")

    def reset_report_settings(self):
        """استعادة الإعدادات الافتراضية"""
        if messagebox.askyesno("تأكيد", "هل تريد استعادة الإعدادات الافتراضية؟"):
            self.lab_name_var.set("مختبر الصحة العامة المركزي - ذي قار")
            self.ministry_name_var.set("وزارة الصحة العراقية")
            self.lab_address_var.set("محافظة ذي قار - العراق")
            self.lab_phone_var.set("")
            self.font_type_var.set("Tahoma")
            self.font_size_var.set("12")
            self.show_logo_var.set(True)
            self.show_date_var.set(True)
            self.show_signature_var.set(True)

    def test_report_settings(self):
        """اختبار إعدادات التقرير"""
        messagebox.showinfo("اختبار التقرير", "سيتم إنشاء تقرير تجريبي لاختبار الإعدادات")

    def refresh_report_settings(self):
        """تحديث إعدادات التقرير من قاعدة البيانات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM report_settings LIMIT 1')
            settings = cursor.fetchone()

            if settings:
                self.lab_name_var.set(settings[1] or "مختبر الصحة العامة المركزي - ذي قار")
                self.ministry_name_var.set(settings[2] or "وزارة الصحة العراقية")
                self.lab_address_var.set(settings[3] or "محافظة ذي قار - العراق")
                self.lab_phone_var.set(settings[4] or "")
                self.font_type_var.set(settings[5] or "Tahoma")
                self.font_size_var.set(str(settings[6]) if settings[6] else "12")
                self.show_logo_var.set(bool(settings[7]) if settings[7] is not None else True)
                self.show_date_var.set(bool(settings[8]) if settings[8] is not None else True)
                self.show_signature_var.set(bool(settings[9]) if settings[9] is not None else True)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحديث إعدادات التقرير: {e}")

    def show(self):
        """إظهار التبويب"""
        self.main_frame.pack(fill='both', expand=True)

    def hide(self):
        """إخفاء التبويب"""
        self.main_frame.pack_forget()
