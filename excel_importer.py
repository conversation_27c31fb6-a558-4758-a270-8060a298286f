import openpyxl
from datetime import datetime
import sqlite3
from tkinter import messagebox

class ExcelImporter:
    def __init__(self, db, sticker_printer):
        self.db = db
        self.sticker_printer = sticker_printer
        
    def import_from_excel(self, file_path):
        """استيراد البيانات من ملف Excel"""
        try:
            # فتح ملف Excel
            workbook = openpyxl.load_workbook(file_path)
            sheet = workbook.active
            
            imported_count = 0
            errors = []
            
            # قراءة الصف الأول للتأكد من العناوين
            headers = [cell.value for cell in sheet[1]]
            expected_headers = [
                'الاسم', 'العمر', 'الجنس', 'العنوان', 'رقم الهاتف',
                'نوع العينة', 'جهة الإرسال', 'تاريخ سحب العينة',
                'رقم الجواز', 'رقم الوصل', 'التحاليل'
            ]
            
            # التحقق من وجود العناوين المطلوبة
            missing_headers = []
            for header in expected_headers[:8]:  # الحقول الإجبارية
                if header not in headers:
                    missing_headers.append(header)
            
            if missing_headers:
                raise Exception(f"العناوين المفقودة في ملف Excel: {', '.join(missing_headers)}")
            
            # إنشاء خريطة للعناوين
            header_map = {header: headers.index(header) for header in headers if header in expected_headers}
            
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # قراءة البيانات من الصف الثاني فما فوق
            for row_num, row in enumerate(sheet.iter_rows(min_row=2, values_only=True), start=2):
                try:
                    # استخراج البيانات
                    patient_data = self.extract_patient_data(row, header_map)
                    
                    # التحقق من صحة البيانات
                    if not self.validate_patient_data(patient_data):
                        errors.append(f"الصف {row_num}: بيانات غير صحيحة")
                        continue
                    
                    # الحصول على الرقم الوطني التالي
                    national_id = self.db.get_next_national_id()
                    
                    # إدراج المريض
                    cursor.execute('''
                        INSERT INTO patients (
                            national_id, name, age, gender, address, phone,
                            passport_number, receipt_number, sample_type,
                            sender_organization, sample_collection_date
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        national_id,
                        patient_data['name'],
                        patient_data['age'],
                        patient_data['gender'],
                        patient_data['address'],
                        patient_data['phone'],
                        patient_data.get('passport'),
                        patient_data.get('receipt'),
                        patient_data['sample_type'],
                        patient_data['sender_org'],
                        patient_data['collection_date']
                    ))
                    
                    patient_id = cursor.lastrowid
                    
                    # إضافة التحاليل
                    tests = patient_data.get('tests', [])
                    for test in tests:
                        cursor.execute(
                            'INSERT INTO patient_tests (patient_id, test_name) VALUES (?, ?)',
                            (patient_id, test.strip())
                        )
                    
                    # طباعة الستيكر
                    try:
                        self.sticker_printer.print_sticker(
                            patient_data['name'],
                            patient_data['sample_type'],
                            national_id
                        )
                    except Exception as print_error:
                        errors.append(f"الصف {row_num}: فشل في طباعة الستيكر - {str(print_error)}")
                    
                    imported_count += 1
                    
                except Exception as row_error:
                    errors.append(f"الصف {row_num}: {str(row_error)}")
                    continue
            
            conn.commit()
            conn.close()
            
            # عرض تقرير الاستيراد
            if errors:
                error_message = f"تم استيراد {imported_count} مريض بنجاح.\n\nالأخطاء:\n" + "\n".join(errors[:10])
                if len(errors) > 10:
                    error_message += f"\n... و {len(errors) - 10} أخطاء أخرى"
                messagebox.showwarning("تحذير", error_message)
            
            return imported_count
            
        except Exception as e:
            raise Exception(f"خطأ في استيراد ملف Excel: {str(e)}")
    
    def extract_patient_data(self, row, header_map):
        """استخراج بيانات المريض من صف Excel"""
        data = {}
        
        # البيانات الأساسية
        data['name'] = str(row[header_map['الاسم']]).strip() if row[header_map['الاسم']] else ""
        data['age'] = int(row[header_map['العمر']]) if row[header_map['العمر']] else 0
        data['gender'] = str(row[header_map['الجنس']]).strip().upper() if row[header_map['الجنس']] else ""
        data['address'] = str(row[header_map['العنوان']]).strip() if row[header_map['العنوان']] else ""
        data['phone'] = str(row[header_map['رقم الهاتف']]).strip() if row[header_map['رقم الهاتف']] else ""
        data['sample_type'] = str(row[header_map['نوع العينة']]).strip() if row[header_map['نوع العينة']] else ""
        data['sender_org'] = str(row[header_map['جهة الإرسال']]).strip() if row[header_map['جهة الإرسال']] else ""
        
        # تاريخ سحب العينة
        collection_date = row[header_map['تاريخ سحب العينة']]
        if isinstance(collection_date, datetime):
            data['collection_date'] = collection_date.strftime("%Y-%m-%d")
        elif collection_date:
            data['collection_date'] = str(collection_date)
        else:
            data['collection_date'] = datetime.now().strftime("%Y-%m-%d")
        
        # البيانات الاختيارية
        if 'رقم الجواز' in header_map and row[header_map['رقم الجواز']]:
            data['passport'] = str(row[header_map['رقم الجواز']]).strip()
        
        if 'رقم الوصل' in header_map and row[header_map['رقم الوصل']]:
            data['receipt'] = str(row[header_map['رقم الوصل']]).strip()
        
        # التحاليل
        if 'التحاليل' in header_map and row[header_map['التحاليل']]:
            tests_str = str(row[header_map['التحاليل']]).strip()
            data['tests'] = [test.strip() for test in tests_str.split(',') if test.strip()]
        else:
            data['tests'] = []
        
        return data
    
    def validate_patient_data(self, data):
        """التحقق من صحة بيانات المريض"""
        # التحقق من الحقول الإجبارية
        required_fields = ['name', 'age', 'gender', 'address', 'phone', 'sample_type', 'sender_org']
        
        for field in required_fields:
            if not data.get(field):
                return False
        
        # التحقق من صحة العمر
        if data['age'] <= 0 or data['age'] > 150:
            return False
        
        # التحقق من صحة الجنس
        if data['gender'] not in ['M', 'F']:
            return False
        
        return True
    
    def create_excel_template(self, file_path):
        """إنشاء قالب Excel للاستيراد"""
        try:
            workbook = openpyxl.Workbook()
            sheet = workbook.active
            sheet.title = "بيانات المرضى"
            
            # إضافة العناوين
            headers = [
                'الاسم', 'العمر', 'الجنس', 'العنوان', 'رقم الهاتف',
                'نوع العينة', 'جهة الإرسال', 'تاريخ سحب العينة',
                'رقم الجواز', 'رقم الوصل', 'التحاليل'
            ]
            
            for col, header in enumerate(headers, 1):
                sheet.cell(row=1, column=col, value=header)
            
            # إضافة بيانات تجريبية
            sample_data = [
                ['أحمد محمد علي', 30, 'M', 'الناصرية - حي الجمهورية', '07801234567',
                 'دم', 'مستشفى الناصرية العام', '2024-01-15', '', '', 'فحص كوفيد-19, فحص الملاريا'],
                ['فاطمة حسن أحمد', 25, 'F', 'الشطرة - حي الأطباء', '07709876543',
                 'بول', 'مركز صحي الشطرة', '2024-01-15', 'A1234567', 'R001', 'فحص السل']
            ]
            
            for row, data in enumerate(sample_data, 2):
                for col, value in enumerate(data, 1):
                    sheet.cell(row=row, column=col, value=value)
            
            # تنسيق الجدول
            for col in range(1, len(headers) + 1):
                sheet.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15
            
            # حفظ الملف
            workbook.save(file_path)
            return True
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء قالب Excel: {str(e)}")
    
    def get_sample_types_from_db(self):
        """الحصول على أنواع العينات من قاعدة البيانات"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT name FROM sample_types ORDER BY name')
        types = [row[0] for row in cursor.fetchall()]
        conn.close()
        return types
    
    def get_sender_organizations_from_db(self):
        """الحصول على جهات الإرسال من قاعدة البيانات"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT name FROM sender_organizations ORDER BY name')
        orgs = [row[0] for row in cursor.fetchall()]
        conn.close()
        return orgs
    
    def get_available_tests_from_db(self):
        """الحصول على التحاليل المتاحة من قاعدة البيانات"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT name FROM available_tests ORDER BY name')
        tests = [row[0] for row in cursor.fetchall()]
        conn.close()
        return tests
