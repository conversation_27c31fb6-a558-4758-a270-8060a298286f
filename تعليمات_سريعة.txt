========================================
برنامج مختبر الصحة العامة المركزي - ذي قار
تعليمات سريعة للاستخدام
========================================

1. تشغيل البرنامج:
   - انقر نقراً مزدوجاً على ملف "run_lab.bat"
   - أو افتح موجه الأوامر وشغل: python main.py

2. الإعداد الأولي:
   أ) اذهب إلى تبويب "الإعدادات"
   ب) أضف أنواع العينات المطلوبة
   ج) أضف جهات الإرسال
   د) أضف التحاليل المتاحة
   هـ) أضف أسماء الفنيين
   و) اختبر إعدادات الطابعة

3. إدخال البيانات:
   أ) اذهب إلى تبويب "إدخال البيانات"
   ب) املأ جميع الحقول الإجبارية (*)
   ج) اختر التحاليل المطلوبة
   د) اضغط "إضافة"
   هـ) سيتم طباعة الستيكر تلقائياً

4. استيراد من Excel:
   أ) استخدم الملف التجريبي "بيانات_تجريبية_للاستيراد.xlsx"
   ب) أو أنشئ ملف Excel بالعناوين المطلوبة
   ج) اضغط "استيراد من Excel" في تبويب إدخال البيانات

5. إنشاء وجبات العمل:
   أ) اذهب إلى تبويب "العمل"
   ب) حدد الفترة الزمنية
   ج) اضغط "إنشاء الوجبة"
   د) حدد الفنيين للعينات
   هـ) اضغط "إرسال إلى النتائج"

6. إدخال النتائج:
   أ) اذهب إلى تبويب "النتائج"
   ب) أدخل رقم الوجبة أو اختر من القائمة
   ج) اضغط "عرض"
   د) أدخل النتائج فردياً أو جماعياً
   هـ) اضغط "حفظ جميع النتائج"

7. التقارير والإحصائيات:
   أ) اذهب إلى تبويب "التقارير والإحصائيات"
   ب) استخدم الفلاتر للبحث
   ج) اضغط "بحث"
   د) اختر نوع التقرير المطلوب
   هـ) اضغط "إحصائيات" للإحصائيات الشاملة

8. نصائح مهمة:
   - احفظ نسخة احتياطية من قاعدة البيانات بانتظام
   - تأكد من تشغيل الطابعة قبل طباعة الستيكر
   - استخدم تنسيق التاريخ: YYYY-MM-DD
   - تأكد من صحة البيانات قبل الحفظ

9. حل المشاكل الشائعة:
   - إذا لم تعمل الطباعة: تحقق من إعدادات الطابعة
   - إذا ظهرت أخطاء: تأكد من تثبيت جميع المكتبات
   - إذا لم يعمل الاستيراد: تحقق من تنسيق ملف Excel

10. الملفات المهمة:
    - lab_database.db: قاعدة البيانات (احفظ نسخة احتياطية)
    - reports/: مجلد التقارير المُنشأة
    - بيانات_تجريبية_للاستيراد.xlsx: ملف تجريبي للاختبار

للدعم الفني أو الاستفسارات، راجع ملف README.md

========================================
