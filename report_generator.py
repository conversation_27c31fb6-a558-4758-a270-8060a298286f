from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.pdfgen import canvas
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
from datetime import datetime
import sqlite3

# إضافة مكتبة لمعالجة النص العربي
try:
    from bidi.algorithm import get_display
    from arabic_reshaper import reshape
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False
    print("تحذير: مكتبات معالجة النص العربي غير متوفرة. سيتم استخدام معالجة بسيطة.")

class ReportGenerator:
    def __init__(self, db):
        self.db = db
        self.reports_dir = "reports"

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        if not os.path.exists(self.reports_dir):
            os.makedirs(self.reports_dir)

        # تحميل الخطوط العربية
        self.setup_arabic_fonts()

        # تحميل إعدادات التقرير
        self.load_report_settings()

    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية مع دعم أفضل للنص العربي"""
        try:
            # قائمة الخطوط العربية بترتيب الأولوية
            font_configs = [
                # خطوط عربية ممتازة
                ('C:/Windows/Fonts/segoeui.ttf', 'SegoeUI', 'C:/Windows/Fonts/segoeuib.ttf', 'SegoeUI-Bold'),
                ('C:/Windows/Fonts/tahoma.ttf', 'Tahoma', 'C:/Windows/Fonts/tahomabd.ttf', 'Tahoma-Bold'),
                ('C:/Windows/Fonts/calibri.ttf', 'Calibri', 'C:/Windows/Fonts/calibrib.ttf', 'Calibri-Bold'),
                ('C:/Windows/Fonts/arial.ttf', 'Arial', 'C:/Windows/Fonts/arialbd.ttf', 'Arial-Bold'),
                # خطوط احتياطية
                ('C:/Windows/Fonts/times.ttf', 'Times', 'C:/Windows/Fonts/timesbd.ttf', 'Times-Bold'),
                ('C:/Windows/Fonts/verdana.ttf', 'Verdana', 'C:/Windows/Fonts/verdanab.ttf', 'Verdana-Bold')
            ]

            self.arabic_font = 'Helvetica'
            self.arabic_font_bold = 'Helvetica-Bold'
            font_loaded = False

            # محاولة تحميل أفضل خط متاح
            for regular_path, regular_name, bold_path, bold_name in font_configs:
                try:
                    # تحميل الخط العادي
                    if os.path.exists(regular_path):
                        pdfmetrics.registerFont(TTFont(regular_name, regular_path))
                        self.arabic_font = regular_name
                        font_loaded = True
                        print(f"تم تحميل الخط العادي: {regular_name}")

                    # تحميل الخط العريض
                    if os.path.exists(bold_path):
                        pdfmetrics.registerFont(TTFont(bold_name, bold_path))
                        self.arabic_font_bold = bold_name
                        print(f"تم تحميل الخط العريض: {bold_name}")

                    # إذا تم تحميل خط واحد على الأقل، توقف
                    if font_loaded:
                        break

                except Exception as font_error:
                    print(f"فشل تحميل الخط {regular_name}: {font_error}")
                    continue

            if not font_loaded:
                print("تحذير: لم يتم العثور على خطوط عربية، سيتم استخدام الخط الافتراضي")
                # محاولة أخيرة مع خطوط النظام الأساسية
                try:
                    # خط Windows الافتراضي للعربية
                    system_fonts = [
                        'C:/Windows/Fonts/segoeui.ttf',
                        'C:/Windows/Fonts/arial.ttf'
                    ]
                    for font_path in system_fonts:
                        if os.path.exists(font_path):
                            pdfmetrics.registerFont(TTFont('SystemArabic', font_path))
                            self.arabic_font = 'SystemArabic'
                            self.arabic_font_bold = 'SystemArabic'
                            print(f"تم تحميل خط النظام: {font_path}")
                            break
                except:
                    pass
            else:
                print(f"تم إعداد الخطوط العربية بنجاح: {self.arabic_font} / {self.arabic_font_bold}")

        except Exception as e:
            print(f"خطأ في إعداد الخطوط العربية: {e}")
            self.arabic_font = 'Helvetica'
            self.arabic_font_bold = 'Helvetica-Bold'

    def process_arabic_text(self, text):
        """معالجة النص العربي لضمان العرض الصحيح"""
        if not text:
            return ""

        try:
            # تحويل النص إلى نص
            text = str(text).strip()

            # فحص وجود أحرف عربية
            has_arabic = any('\u0600' <= char <= '\u06FF' for char in text)

            if has_arabic and ARABIC_SUPPORT:
                # استخدام مكتبات معالجة النص العربي المتقدمة
                try:
                    # إعادة تشكيل النص العربي
                    reshaped_text = reshape(text)
                    # تطبيق خوارزمية الاتجاه الثنائي
                    bidi_text = get_display(reshaped_text)
                    return bidi_text
                except Exception as e:
                    print(f"خطأ في معالجة النص العربي المتقدمة: {e}")
                    # العودة للمعالجة البسيطة
                    return f"\u202E{text}\u202C"

            elif has_arabic:
                # معالجة بسيطة للنص العربي
                # استخدام علامات التحكم في الاتجاه
                return f"\u202E{text}\u202C"  # Right-to-Left Override

            else:
                # نص إنجليزي أو أرقام
                return text

        except Exception as e:
            print(f"خطأ في معالجة النص: {e}")
            return str(text)

    def load_report_settings(self):
        """تحميل إعدادات التقرير من قاعدة البيانات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM report_settings LIMIT 1')
            settings = cursor.fetchone()

            if settings:
                self.lab_name = settings[1] or "مختبر الصحة العامة المركزي - ذي قار"
                self.ministry_name = settings[2] or "وزارة الصحة العراقية"
                self.lab_address = settings[3] or "محافظة ذي قار - العراق"
                self.lab_phone = settings[4] or ""
                self.report_font_type = settings[5] or "Tahoma"
                self.report_font_size = settings[6] or 12
                self.show_logo = bool(settings[7]) if settings[7] is not None else True
                self.show_date = bool(settings[8]) if settings[8] is not None else True
                self.show_signature = bool(settings[9]) if settings[9] is not None else True
            else:
                # الإعدادات الافتراضية
                self.lab_name = "مختبر الصحة العامة المركزي - ذي قار"
                self.ministry_name = "وزارة الصحة العراقية"
                self.lab_address = "محافظة ذي قار - العراق"
                self.lab_phone = ""
                self.report_font_type = "Tahoma"
                self.report_font_size = 12
                self.show_logo = True
                self.show_date = True
                self.show_signature = True

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل إعدادات التقرير: {e}")
            # استخدام الإعدادات الافتراضية
            self.lab_name = "مختبر الصحة العامة المركزي - ذي قار"
            self.ministry_name = "وزارة الصحة العراقية"
            self.lab_address = "محافظة ذي قار - العراق"
            self.lab_phone = ""
            self.report_font_type = "Tahoma"
            self.report_font_size = 12
            self.show_logo = True
            self.show_date = True
            self.show_signature = True

    def get_arabic_styles(self):
        """إنشاء أنماط النصوص العربية محسنة"""
        styles = getSampleStyleSheet()

        # نمط العنوان الرئيسي
        title_style = ParagraphStyle(
            'ArabicTitle',
            parent=styles['Title'],
            fontName=self.arabic_font_bold,
            fontSize=self.report_font_size + 6,
            alignment=TA_CENTER,
            spaceAfter=20,
            textColor=colors.darkblue,
            leading=self.report_font_size + 10,  # تباعد الأسطر
            wordWrap='RTL'  # التفاف النص من اليمين لليسار
        )

        # نمط العنوان الفرعي
        heading_style = ParagraphStyle(
            'ArabicHeading',
            parent=styles['Heading1'],
            fontName=self.arabic_font_bold,
            fontSize=self.report_font_size + 2,
            alignment=TA_RIGHT,
            spaceAfter=12,
            textColor=colors.black,
            leading=self.report_font_size + 6,
            rightIndent=0,
            leftIndent=0
        )

        # نمط النص العادي
        normal_style = ParagraphStyle(
            'ArabicNormal',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=self.report_font_size,
            alignment=TA_RIGHT,
            spaceAfter=6,
            leading=self.report_font_size + 4,
            rightIndent=0,
            leftIndent=0
        )

        # نمط النص الصغير
        body_style = ParagraphStyle(
            'ArabicBody',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=self.report_font_size - 2,
            alignment=TA_RIGHT,
            spaceAfter=4,
            leading=self.report_font_size + 2,
            rightIndent=0,
            leftIndent=0
        )

        # نمط خاص للجداول
        table_style = ParagraphStyle(
            'ArabicTable',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=self.report_font_size - 1,
            alignment=TA_CENTER,
            spaceAfter=2,
            leading=self.report_font_size + 2
        )

        return {
            'title': title_style,
            'heading': heading_style,
            'normal': normal_style,
            'body': body_style,
            'table': table_style
        }

    def create_arabic_paragraph(self, text, style):
        """إنشاء فقرة عربية محسنة"""
        try:
            # معالجة النص العربي
            processed_text = self.process_arabic_text(text)

            # إنشاء الفقرة مع معالجة خاصة للعربية
            paragraph = Paragraph(processed_text, style)
            return paragraph

        except Exception as e:
            print(f"خطأ في إنشاء الفقرة العربية: {e}")
            # العودة للطريقة العادية
            return Paragraph(str(text), style)

    def create_arabic_table_data(self, data):
        """معالجة بيانات الجدول للنص العربي"""
        try:
            processed_data = []
            for row in data:
                processed_row = []
                for cell in row:
                    if isinstance(cell, str):
                        processed_row.append(self.process_arabic_text(cell))
                    else:
                        processed_row.append(str(cell))
                processed_data.append(processed_row)
            return processed_data
        except Exception as e:
            print(f"خطأ في معالجة بيانات الجدول: {e}")
            return data

    def generate_individual_report(self, national_id):
        """إنشاء تقرير فردي لمريض"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # الحصول على بيانات المريض
            cursor.execute('''
                SELECT * FROM patients WHERE national_id = ?
            ''', (national_id,))
            
            patient = cursor.fetchone()
            
            if not patient:
                raise Exception("لم يتم العثور على المريض")
            
            # الحصول على التحاليل والنتائج
            cursor.execute('''
                SELECT pt.test_name, tr.result, tr.created_at
                FROM patient_tests pt
                LEFT JOIN test_results tr ON (pt.patient_id = tr.patient_id AND pt.test_name = tr.test_name)
                WHERE pt.patient_id = ?
                ORDER BY pt.test_name
            ''', (patient[0],))
            
            tests_results = cursor.fetchall()
            conn.close()
            
            # إنشاء ملف PDF
            filename = f"تقرير_فردي_{national_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(self.reports_dir, filename)
            
            doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

            # إعداد الأنماط العربية
            arabic_styles = self.get_arabic_styles()
            
            # بناء محتوى التقرير
            story = []
            
            # شعار وعنوان المختبر
            if self.show_logo:
                story.append(self.create_arabic_paragraph(self.ministry_name, arabic_styles['title']))
                story.append(self.create_arabic_paragraph(self.lab_name, arabic_styles['title']))
                if self.lab_address:
                    story.append(self.create_arabic_paragraph(self.lab_address, arabic_styles['body']))
                if self.lab_phone:
                    story.append(self.create_arabic_paragraph(f"هاتف: {self.lab_phone}", arabic_styles['body']))
                story.append(Spacer(1, 20))

            # عنوان التقرير
            story.append(self.create_arabic_paragraph("تقرير نتائج التحاليل المختبرية", arabic_styles['heading']))
            story.append(Spacer(1, 20))

            # معلومات المريض
            story.append(self.create_arabic_paragraph("معلومات المريض:", arabic_styles['heading']))

            # بيانات المريض مع معالجة عربية محسنة (من اليمين لليسار)
            patient_info_raw = [
                [str(patient[1]), "الرقم الوطني:"],
                [patient[2], "الاسم:"],
                [f"{patient[3]} سنة", "العمر:"],
                ["ذكر" if patient[4] == 'M' else "أنثى", "الجنس:"],
                [patient[5], "العنوان:"],
                [str(patient[6]), "رقم الهاتف:"],
                [patient[9], "نوع العينة:"],
                [patient[10], "جهة الإرسال:"],
                [str(patient[11]), "تاريخ سحب العينة:"],
                [str(patient[12]), "تاريخ استلام العينة:"]
            ]

            # معالجة البيانات للعربية
            patient_info = self.create_arabic_table_data(patient_info_raw)
            
            # إضافة رقم الجواز ورقم الوصل إذا كانا متوفرين (مع عكس الأعمدة)
            if patient[7]:
                patient_info.append([patient[7], "رقم الجواز:"])
            if patient[8]:
                patient_info.append([patient[8], "رقم الوصل:"])

            # جدول معلومات المريض (من اليمين لليسار)
            patient_table = Table(patient_info, colWidths=[3*inch, 2*inch])
            patient_table.setStyle(TableStyle([
                ('BACKGROUND', (1, 0), (1, -1), colors.lightgrey),  # عكس العمود
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (0, -1), 'LEFT'),   # البيانات على اليسار
                ('ALIGN', (1, 0), (1, -1), 'RIGHT'),  # التسميات على اليمين
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 11),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('BACKGROUND', (0, 0), (0, -1), colors.beige),  # عكس العمود
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(patient_table)
            story.append(Spacer(1, 30))

            # نتائج التحاليل
            story.append(self.create_arabic_paragraph("نتائج التحاليل:", arabic_styles['heading']))

            if tests_results:
                # إعداد بيانات الجدول (من اليمين لليسار)
                tests_data_raw = [
                    ["تاريخ النتيجة", "النتيجة", "التحليل"]
                ]

                for test_name, result, result_date in tests_results:
                    result_text = result if result else "لم يتم إدخال النتيجة"
                    date_text = str(result_date) if result_date else ""
                    tests_data_raw.append([date_text, result_text, test_name])

                # معالجة البيانات للعربية
                tests_data = self.create_arabic_table_data(tests_data_raw)

                tests_table = Table(tests_data, colWidths=[2*inch, 1.5*inch, 2.5*inch])
                tests_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), self.arabic_font_bold),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
                    ('FONTSIZE', (0, 1), (-1, -1), 11),
                ]))

                story.append(tests_table)
            else:
                story.append(self.create_arabic_paragraph("لا توجد تحاليل مسجلة لهذا المريض", arabic_styles['normal']))

            story.append(Spacer(1, 30))

            # تاريخ إصدار التقرير
            if self.show_date:
                story.append(self.create_arabic_paragraph(f"تاريخ إصدار التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}", arabic_styles['body']))

            # توقيع المختبر
            if self.show_signature:
                story.append(Spacer(1, 20))
                story.append(self.create_arabic_paragraph(self.lab_name, arabic_styles['body']))
                story.append(self.create_arabic_paragraph(self.ministry_name, arabic_styles['body']))
            
            # بناء PDF
            doc.build(story)
            
            return filepath
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء التقرير الفردي: {str(e)}")
    
    def generate_group_report(self, national_ids, start_date, end_date):
        """إنشاء تقرير جماعي"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # إنشاء ملف PDF
            filename = f"تقرير_جماعي_{start_date}_إلى_{end_date}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(self.reports_dir, filename)
            
            doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

            # إعداد الأنماط العربية
            arabic_styles = self.get_arabic_styles()
            
            # بناء محتوى التقرير
            story = []
            
            # شعار وعنوان المختبر
            if self.show_logo:
                story.append(self.create_arabic_paragraph(self.ministry_name, arabic_styles['title']))
                story.append(self.create_arabic_paragraph(self.lab_name, arabic_styles['title']))
                if self.lab_address:
                    story.append(self.create_arabic_paragraph(self.lab_address, arabic_styles['body']))
                if self.lab_phone:
                    story.append(self.create_arabic_paragraph(f"هاتف: {self.lab_phone}", arabic_styles['body']))
                story.append(Spacer(1, 20))

            # عنوان التقرير
            story.append(self.create_arabic_paragraph("التقرير الجماعي لنتائج التحاليل", arabic_styles['heading']))
            story.append(self.create_arabic_paragraph(f"الفترة من {start_date} إلى {end_date}", arabic_styles['heading']))
            story.append(Spacer(1, 20))
            
            # الحصول على البيانات
            placeholders = ','.join(['?' for _ in national_ids])
            query = f'''
                SELECT p.national_id, p.name, p.age, p.gender, p.sample_type, 
                       p.sender_organization, p.sample_collection_date,
                       GROUP_CONCAT(DISTINCT pt.test_name) as tests,
                       GROUP_CONCAT(DISTINCT tr.result) as results
                FROM patients p
                LEFT JOIN patient_tests pt ON p.id = pt.patient_id
                LEFT JOIN test_results tr ON (p.id = tr.patient_id AND pt.test_name = tr.test_name)
                WHERE p.national_id IN ({placeholders})
                GROUP BY p.id
                ORDER BY p.national_id
            '''
            
            cursor.execute(query, national_ids)
            patients_data = cursor.fetchall()
            
            if patients_data:
                # جدول البيانات (من اليمين لليسار)
                table_data_raw = [["النتائج", "التحاليل", "جهة الإرسال", "نوع العينة", "الجنس", "العمر", "الاسم", "الرقم الوطني"]]

                for row in patients_data:
                    gender_text = "ذكر" if row[3] == 'M' else "أنثى"
                    tests = row[7] if row[7] else "لا توجد"
                    results = row[8] if row[8] else "لا توجد"

                    table_data_raw.append([
                        results, tests, row[5], row[4],
                        gender_text, str(row[2]), row[1], str(row[0])
                    ])

                # معالجة البيانات للعربية
                table_data = self.create_arabic_table_data(table_data_raw)

                # إنشاء الجدول (مع عكس عرض الأعمدة)
                table = Table(table_data, colWidths=[1*inch, 1.2*inch, 1.2*inch, 1*inch, 0.6*inch, 0.6*inch, 1.2*inch, 0.8*inch])
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), self.arabic_font_bold),
                    ('FONTSIZE', (0, 0), (-1, 0), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ]))
                
                story.append(table)
            else:
                story.append(self.create_arabic_paragraph("لا توجد بيانات للعرض", arabic_styles['normal']))

            story.append(Spacer(1, 30))

            # إحصائيات سريعة
            story.append(self.create_arabic_paragraph("ملخص الإحصائيات:", arabic_styles['heading']))

            total_patients = len(patients_data)
            story.append(self.create_arabic_paragraph(f"إجمالي المرضى: {total_patients}", arabic_styles['normal']))

            # تاريخ إصدار التقرير
            if self.show_date:
                story.append(Spacer(1, 20))
                story.append(self.create_arabic_paragraph(f"تاريخ إصدار التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}", arabic_styles['body']))

            # توقيع المختبر
            if self.show_signature:
                story.append(Spacer(1, 20))
                story.append(self.create_arabic_paragraph(self.lab_name, arabic_styles['body']))
                story.append(self.create_arabic_paragraph(self.ministry_name, arabic_styles['body']))
            
            conn.close()
            
            # بناء PDF
            doc.build(story)
            
            return filepath
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء التقرير الجماعي: {str(e)}")
    
    def generate_statistics_report(self, stats):
        """إنشاء تقرير الإحصائيات"""
        try:
            # إنشاء ملف PDF
            filename = f"تقرير_إحصائيات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(self.reports_dir, filename)
            
            doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

            # إعداد الأنماط العربية
            arabic_styles = self.get_arabic_styles()
            
            # بناء محتوى التقرير
            story = []
            
            # شعار وعنوان المختبر
            if self.show_logo:
                story.append(self.create_arabic_paragraph(self.ministry_name, arabic_styles['title']))
                story.append(self.create_arabic_paragraph(self.lab_name, arabic_styles['title']))
                if self.lab_address:
                    story.append(self.create_arabic_paragraph(self.lab_address, arabic_styles['body']))
                if self.lab_phone:
                    story.append(self.create_arabic_paragraph(f"هاتف: {self.lab_phone}", arabic_styles['body']))
                story.append(Spacer(1, 20))

            # عنوان التقرير
            story.append(self.create_arabic_paragraph("تقرير الإحصائيات العامة", arabic_styles['heading']))
            story.append(Spacer(1, 20))

            # الإحصائيات العامة
            story.append(self.create_arabic_paragraph("الإحصائيات العامة:", arabic_styles['heading']))

            # جدول الإحصائيات (من اليمين لليسار)
            general_data_raw = [
                ["العدد", "البيان"],
                [str(stats['total_patients']), "إجمالي المرضى"],
                [str(stats['total_samples']), "إجمالي العينات"],
                [str(stats['total_tests']), "إجمالي التحاليل"],
                [str(stats['positive_results']), "النتائج الإيجابية"],
                [str(stats['negative_results']), "النتائج السلبية"],
                [str(stats['retest_results']), "التحاليل المعادة"]
            ]

            # معالجة البيانات للعربية
            general_data = self.create_arabic_table_data(general_data_raw)

            general_table = Table(general_data, colWidths=[2*inch, 3*inch])
            general_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), self.arabic_font_bold),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 1), (-1, -1), 11),
            ]))

            story.append(general_table)
            story.append(Spacer(1, 30))

            # إحصائيات حسب نوع العينة
            if stats['samples_by_type']:
                story.append(self.create_arabic_paragraph("إحصائيات حسب نوع العينة:", arabic_styles['heading']))

                # جدول إحصائيات العينات (من اليمين لليسار)
                sample_data_raw = [["النسبة المئوية", "العدد", "نوع العينة"]]
                for sample_type, count in stats['samples_by_type'].items():
                    percentage = (count / stats['total_samples'] * 100) if stats['total_samples'] > 0 else 0
                    sample_data_raw.append([f"{percentage:.1f}%", str(count), sample_type])

                # معالجة البيانات للعربية
                sample_data = self.create_arabic_table_data(sample_data_raw)

                sample_table = Table(sample_data, colWidths=[1.5*inch, 1.5*inch, 2*inch])
                sample_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), self.arabic_font_bold),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
                    ('FONTSIZE', (0, 1), (-1, -1), 11),
                ]))

                story.append(sample_table)
                story.append(Spacer(1, 30))

            # تاريخ إصدار التقرير
            if self.show_date:
                story.append(self.create_arabic_paragraph(f"تاريخ إصدار التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}", arabic_styles['body']))

            # توقيع المختبر
            if self.show_signature:
                story.append(Spacer(1, 20))
                story.append(self.create_arabic_paragraph(self.lab_name, arabic_styles['body']))
                story.append(self.create_arabic_paragraph(self.ministry_name, arabic_styles['body']))
            
            # بناء PDF
            doc.build(story)
            
            return filepath
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء تقرير الإحصائيات: {str(e)}")
