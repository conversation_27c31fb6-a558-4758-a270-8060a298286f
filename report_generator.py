from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.pdfgen import canvas
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
from datetime import datetime
import sqlite3

class ReportGenerator:
    def __init__(self, db):
        self.db = db
        self.reports_dir = "reports"

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        if not os.path.exists(self.reports_dir):
            os.makedirs(self.reports_dir)

        # تحميل الخطوط العربية
        self.setup_arabic_fonts()

    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة تحميل خط عربي من النظام
            # خط Tahoma يدعم العربية وموجود في معظم أنظمة Windows
            font_paths = [
                'C:/Windows/Fonts/tahoma.ttf',
                'C:/Windows/Fonts/tahomabd.ttf',
                'C:/Windows/Fonts/arial.ttf',
                'C:/Windows/Fonts/arialbd.ttf'
            ]

            self.arabic_font = 'Helvetica'  # خط افتراضي
            self.arabic_font_bold = 'Helvetica-Bold'

            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        if 'tahoma' in font_path.lower():
                            if 'bd' in font_path.lower():
                                pdfmetrics.registerFont(TTFont('Arabic-Bold', font_path))
                                self.arabic_font_bold = 'Arabic-Bold'
                            else:
                                pdfmetrics.registerFont(TTFont('Arabic', font_path))
                                self.arabic_font = 'Arabic'
                        elif 'arial' in font_path.lower():
                            if 'bd' in font_path.lower():
                                pdfmetrics.registerFont(TTFont('Arial-Bold', font_path))
                                self.arabic_font_bold = 'Arial-Bold'
                            else:
                                pdfmetrics.registerFont(TTFont('Arial', font_path))
                                self.arabic_font = 'Arial'
                        break
                    except:
                        continue

            print(f"تم تحميل الخط العربي: {self.arabic_font}")

        except Exception as e:
            print(f"تحذير: لم يتم تحميل الخط العربي: {e}")
            self.arabic_font = 'Helvetica'
            self.arabic_font_bold = 'Helvetica-Bold'

    def get_arabic_styles(self):
        """إنشاء أنماط النصوص العربية"""
        styles = getSampleStyleSheet()

        # نمط العنوان الرئيسي
        title_style = ParagraphStyle(
            'ArabicTitle',
            parent=styles['Title'],
            fontName=self.arabic_font_bold,
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=20,
            textColor=colors.darkblue
        )

        # نمط العنوان الفرعي
        heading_style = ParagraphStyle(
            'ArabicHeading',
            parent=styles['Heading1'],
            fontName=self.arabic_font_bold,
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=12,
            textColor=colors.black
        )

        # نمط النص العادي
        normal_style = ParagraphStyle(
            'ArabicNormal',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=12,
            alignment=TA_RIGHT,
            spaceAfter=6
        )

        # نمط النص المتوسط
        body_style = ParagraphStyle(
            'ArabicBody',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=10,
            alignment=TA_RIGHT,
            spaceAfter=4
        )

        return {
            'title': title_style,
            'heading': heading_style,
            'normal': normal_style,
            'body': body_style
        }

    def generate_individual_report(self, national_id):
        """إنشاء تقرير فردي لمريض"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # الحصول على بيانات المريض
            cursor.execute('''
                SELECT * FROM patients WHERE national_id = ?
            ''', (national_id,))
            
            patient = cursor.fetchone()
            
            if not patient:
                raise Exception("لم يتم العثور على المريض")
            
            # الحصول على التحاليل والنتائج
            cursor.execute('''
                SELECT pt.test_name, tr.result, tr.created_at
                FROM patient_tests pt
                LEFT JOIN test_results tr ON (pt.patient_id = tr.patient_id AND pt.test_name = tr.test_name)
                WHERE pt.patient_id = ?
                ORDER BY pt.test_name
            ''', (patient[0],))
            
            tests_results = cursor.fetchall()
            conn.close()
            
            # إنشاء ملف PDF
            filename = f"تقرير_فردي_{national_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(self.reports_dir, filename)
            
            doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

            # إعداد الأنماط العربية
            arabic_styles = self.get_arabic_styles()
            
            # بناء محتوى التقرير
            story = []
            
            # شعار وعنوان المختبر
            story.append(Paragraph("وزارة الصحة العراقية", arabic_styles['title']))
            story.append(Paragraph("مختبر الصحة العامة المركزي - ذي قار", arabic_styles['title']))
            story.append(Spacer(1, 20))

            # عنوان التقرير
            story.append(Paragraph("تقرير نتائج التحاليل المختبرية", arabic_styles['heading']))
            story.append(Spacer(1, 20))

            # معلومات المريض
            story.append(Paragraph("معلومات المريض:", arabic_styles['heading']))
            
            patient_info = [
                ["الرقم الوطني:", str(patient[1])],
                ["الاسم:", patient[2]],
                ["العمر:", f"{patient[3]} سنة"],
                ["الجنس:", "ذكر" if patient[4] == 'M' else "أنثى"],
                ["العنوان:", patient[5]],
                ["رقم الهاتف:", patient[6]],
                ["نوع العينة:", patient[9]],
                ["جهة الإرسال:", patient[10]],
                ["تاريخ سحب العينة:", patient[11]],
                ["تاريخ استلام العينة:", patient[12]]
            ]
            
            # إضافة رقم الجواز ورقم الوصل إذا كانا متوفرين
            if patient[7]:
                patient_info.append(["رقم الجواز:", patient[7]])
            if patient[8]:
                patient_info.append(["رقم الوصل:", patient[8]])
            
            # جدول معلومات المريض
            patient_table = Table(patient_info, colWidths=[2*inch, 3*inch])
            patient_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 11),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('BACKGROUND', (1, 0), (1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(patient_table)
            story.append(Spacer(1, 30))

            # نتائج التحاليل
            story.append(Paragraph("نتائج التحاليل:", arabic_styles['heading']))
            
            if tests_results:
                tests_data = [["التحليل", "النتيجة", "تاريخ النتيجة"]]
                
                for test_name, result, result_date in tests_results:
                    result_text = result if result else "لم يتم إدخال النتيجة"
                    date_text = result_date if result_date else ""
                    tests_data.append([test_name, result_text, date_text])
                
                tests_table = Table(tests_data, colWidths=[2.5*inch, 1.5*inch, 2*inch])
                tests_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), self.arabic_font_bold),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
                    ('FONTSIZE', (0, 1), (-1, -1), 11),
                ]))

                story.append(tests_table)
            else:
                story.append(Paragraph("لا توجد تحاليل مسجلة لهذا المريض", arabic_styles['normal']))

            story.append(Spacer(1, 30))

            # تاريخ إصدار التقرير
            story.append(Paragraph(f"تاريخ إصدار التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}", arabic_styles['body']))

            # توقيع المختبر
            story.append(Spacer(1, 20))
            story.append(Paragraph("مختبر الصحة العامة المركزي - ذي قار", arabic_styles['body']))
            story.append(Paragraph("وزارة الصحة العراقية", arabic_styles['body']))
            
            # بناء PDF
            doc.build(story)
            
            return filepath
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء التقرير الفردي: {str(e)}")
    
    def generate_group_report(self, national_ids, start_date, end_date):
        """إنشاء تقرير جماعي"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # إنشاء ملف PDF
            filename = f"تقرير_جماعي_{start_date}_إلى_{end_date}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(self.reports_dir, filename)
            
            doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

            # إعداد الأنماط العربية
            arabic_styles = self.get_arabic_styles()
            
            # بناء محتوى التقرير
            story = []
            
            # شعار وعنوان المختبر
            story.append(Paragraph("وزارة الصحة العراقية", arabic_styles['title']))
            story.append(Paragraph("مختبر الصحة العامة المركزي - ذي قار", arabic_styles['title']))
            story.append(Spacer(1, 20))

            # عنوان التقرير
            story.append(Paragraph("التقرير الجماعي لنتائج التحاليل", arabic_styles['heading']))
            story.append(Paragraph(f"الفترة من {start_date} إلى {end_date}", arabic_styles['heading']))
            story.append(Spacer(1, 20))
            
            # الحصول على البيانات
            placeholders = ','.join(['?' for _ in national_ids])
            query = f'''
                SELECT p.national_id, p.name, p.age, p.gender, p.sample_type, 
                       p.sender_organization, p.sample_collection_date,
                       GROUP_CONCAT(DISTINCT pt.test_name) as tests,
                       GROUP_CONCAT(DISTINCT tr.result) as results
                FROM patients p
                LEFT JOIN patient_tests pt ON p.id = pt.patient_id
                LEFT JOIN test_results tr ON (p.id = tr.patient_id AND pt.test_name = tr.test_name)
                WHERE p.national_id IN ({placeholders})
                GROUP BY p.id
                ORDER BY p.national_id
            '''
            
            cursor.execute(query, national_ids)
            patients_data = cursor.fetchall()
            
            if patients_data:
                # جدول البيانات
                table_data = [["الرقم الوطني", "الاسم", "العمر", "الجنس", "نوع العينة", "جهة الإرسال", "التحاليل", "النتائج"]]
                
                for row in patients_data:
                    gender_text = "ذكر" if row[3] == 'M' else "أنثى"
                    tests = row[7] if row[7] else "لا توجد"
                    results = row[8] if row[8] else "لا توجد"
                    
                    table_data.append([
                        str(row[0]), row[1], str(row[2]), gender_text, 
                        row[4], row[5], tests, results
                    ])
                
                # إنشاء الجدول
                table = Table(table_data, colWidths=[0.8*inch, 1.2*inch, 0.6*inch, 0.6*inch, 1*inch, 1.2*inch, 1.2*inch, 1*inch])
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), self.arabic_font_bold),
                    ('FONTSIZE', (0, 0), (-1, 0), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ]))
                
                story.append(table)
            else:
                story.append(Paragraph("لا توجد بيانات للعرض", arabic_styles['normal']))

            story.append(Spacer(1, 30))

            # إحصائيات سريعة
            story.append(Paragraph("ملخص الإحصائيات:", arabic_styles['heading']))

            total_patients = len(patients_data)
            story.append(Paragraph(f"إجمالي المرضى: {total_patients}", arabic_styles['normal']))

            # تاريخ إصدار التقرير
            story.append(Spacer(1, 20))
            story.append(Paragraph(f"تاريخ إصدار التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}", arabic_styles['body']))

            # توقيع المختبر
            story.append(Spacer(1, 20))
            story.append(Paragraph("مختبر الصحة العامة المركزي - ذي قار", arabic_styles['body']))
            story.append(Paragraph("وزارة الصحة العراقية", arabic_styles['body']))
            
            conn.close()
            
            # بناء PDF
            doc.build(story)
            
            return filepath
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء التقرير الجماعي: {str(e)}")
    
    def generate_statistics_report(self, stats):
        """إنشاء تقرير الإحصائيات"""
        try:
            # إنشاء ملف PDF
            filename = f"تقرير_إحصائيات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(self.reports_dir, filename)
            
            doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

            # إعداد الأنماط العربية
            arabic_styles = self.get_arabic_styles()
            
            # بناء محتوى التقرير
            story = []
            
            # شعار وعنوان المختبر
            story.append(Paragraph("وزارة الصحة العراقية", arabic_styles['title']))
            story.append(Paragraph("مختبر الصحة العامة المركزي - ذي قار", arabic_styles['title']))
            story.append(Spacer(1, 20))

            # عنوان التقرير
            story.append(Paragraph("تقرير الإحصائيات العامة", arabic_styles['heading']))
            story.append(Spacer(1, 20))

            # الإحصائيات العامة
            story.append(Paragraph("الإحصائيات العامة:", arabic_styles['heading']))
            
            general_data = [
                ["البيان", "العدد"],
                ["إجمالي المرضى", str(stats['total_patients'])],
                ["إجمالي العينات", str(stats['total_samples'])],
                ["إجمالي التحاليل", str(stats['total_tests'])],
                ["النتائج الإيجابية", str(stats['positive_results'])],
                ["النتائج السلبية", str(stats['negative_results'])],
                ["التحاليل المعادة", str(stats['retest_results'])]
            ]
            
            general_table = Table(general_data, colWidths=[3*inch, 2*inch])
            general_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), self.arabic_font_bold),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 1), (-1, -1), 11),
            ]))

            story.append(general_table)
            story.append(Spacer(1, 30))

            # إحصائيات حسب نوع العينة
            if stats['samples_by_type']:
                story.append(Paragraph("إحصائيات حسب نوع العينة:", arabic_styles['heading']))
                
                sample_data = [["نوع العينة", "العدد", "النسبة المئوية"]]
                for sample_type, count in stats['samples_by_type'].items():
                    percentage = (count / stats['total_samples'] * 100) if stats['total_samples'] > 0 else 0
                    sample_data.append([sample_type, str(count), f"{percentage:.1f}%"])
                
                sample_table = Table(sample_data, colWidths=[2*inch, 1.5*inch, 1.5*inch])
                sample_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), self.arabic_font_bold),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
                    ('FONTSIZE', (0, 1), (-1, -1), 11),
                ]))

                story.append(sample_table)
                story.append(Spacer(1, 30))

            # تاريخ إصدار التقرير
            story.append(Paragraph(f"تاريخ إصدار التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}", arabic_styles['body']))

            # توقيع المختبر
            story.append(Spacer(1, 20))
            story.append(Paragraph("مختبر الصحة العامة المركزي - ذي قار", arabic_styles['body']))
            story.append(Paragraph("وزارة الصحة العراقية", arabic_styles['body']))
            
            # بناء PDF
            doc.build(story)
            
            return filepath
            
        except Exception as e:
            raise Exception(f"خطأ في إنشاء تقرير الإحصائيات: {str(e)}")
