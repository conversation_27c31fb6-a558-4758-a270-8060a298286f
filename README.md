# برنامج مختبر الصحة العامة المركزي - ذي قار

نظام إدارة شامل للمختبر يشمل إدخال البيانات، إدارة الوجبات، النتائج، التقارير والإعدادات.

## المميزات

### 1. تبويب إدخال البيانات
- إدخال بيانات المرضى والعينات
- التحقق من صحة البيانات
- طباعة ستيكر تلقائية
- استيراد البيانات من ملفات Excel
- البحث والتعديل والحذف

### 2. تبويب العمل
- إنشاء وجبات العمل حسب الفترات الزمنية
- تحديد الفنيين المسؤولين
- إرسال الوجبات إلى تبويب النتائج

### 3. تبويب النتائج
- إدخال نتائج التحاليل حسب رقم الوجبة
- نتائج متعددة: Negative, Positive, Retest, Recollection, Sent, TND
- تطبيق النتائج بشكل فردي أو جماعي

### 4. تبويب التقارير والإحصائيات
- بحث وفلترة متقدمة
- تقارير فردية وجماعية
- إحصائيات شاملة
- طباعة التقارير مع شعار الوزارة
- البحث بالباركود

### 5. تبويب الإعدادات
- إدارة أنواع العينات
- إدارة جهات الإرسال
- إدارة التحاليل المتاحة
- إدارة الفنيين
- إعدادات الطابعة

## متطلبات النظام

- Windows 10 أو أحدث
- Python 3.8 أو أحدث
- طابعة (للستيكر والتقارير)

## التثبيت

### 1. تثبيت Python
قم بتحميل وتثبيت Python من الموقع الرسمي:
https://www.python.org/downloads/

### 2. تثبيت المكتبات المطلوبة
افتح موجه الأوامر (Command Prompt) وقم بتشغيل الأمر التالي:

```bash
pip install -r requirements.txt
```

أو قم بتثبيت المكتبات يدوياً:

```bash
pip install openpyxl reportlab Pillow pywin32
```

### 3. تشغيل البرنامج
قم بتشغيل الملف الرئيسي:

```bash
python main.py
```

## كيفية الاستخدام

### البدء السريع

1. **إعداد النظام**: ابدأ بتبويب الإعدادات لإضافة أنواع العينات وجهات الإرسال والتحاليل والفنيين
2. **إدخال البيانات**: استخدم تبويب إدخال البيانات لإضافة المرضى والعينات
3. **إنشاء الوجبات**: في تبويب العمل، قم بإنشاء وجبات عمل وتحديد الفنيين
4. **إدخال النتائج**: في تبويب النتائج، قم بإدخال نتائج التحاليل
5. **التقارير**: استخدم تبويب التقارير لإنشاء التقارير والإحصائيات

### إدخال البيانات

1. املأ جميع الحقول الإجبارية (المميزة بـ *)
2. اختر التحاليل المطلوبة
3. اضغط "إضافة" لحفظ البيانات
4. سيتم طباعة الستيكر تلقائياً

### استيراد من Excel

1. قم بإعداد ملف Excel بالعناوين التالية:
   - الاسم، العمر، الجنس، العنوان، رقم الهاتف
   - نوع العينة، جهة الإرسال، تاريخ سحب العينة
   - رقم الجواز، رقم الوصل، التحاليل

2. اضغط "استيراد من Excel" واختر الملف
3. سيتم استيراد البيانات وطباعة الستيكر لكل عينة

### إنشاء الوجبات

1. حدد الفترة الزمنية (من تاريخ - إلى تاريخ)
2. اضغط "إنشاء الوجبة"
3. حدد الفنيين المسؤولين عن كل عينة
4. اضغط "إرسال إلى النتائج" عند الانتهاء

### إدخال النتائج

1. أدخل رقم الوجبة أو اختر من القائمة
2. اضغط "عرض" لإظهار العينات
3. حدد النتائج بشكل فردي أو جماعي
4. اضغط "حفظ جميع النتائج"

### التقارير

1. استخدم الفلاتر للبحث (التاريخ، الاسم، نوع العينة، إلخ)
2. اضغط "بحث" لعرض النتائج
3. اختر "تقرير فردي" أو "تقرير جماعي"
4. اضغط "إحصائيات" لعرض الإحصائيات الشاملة

## الملفات المهمة

- `main.py`: الملف الرئيسي لتشغيل البرنامج
- `database.py`: إدارة قاعدة البيانات
- `main_window.py`: النافذة الرئيسية والتبويبات
- `data_entry_tab.py`: تبويب إدخال البيانات
- `work_tab.py`: تبويب العمل
- `results_tab.py`: تبويب النتائج
- `reports_tab.py`: تبويب التقارير
- `settings_tab.py`: تبويب الإعدادات
- `sticker_printer.py`: نظام طباعة الستيكر
- `excel_importer.py`: استيراد البيانات من Excel
- `report_generator.py`: إنشاء التقارير PDF

## قاعدة البيانات

يستخدم البرنامج قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:

- `patients`: بيانات المرضى والعينات
- `patient_tests`: التحاليل المطلوبة لكل مريض
- `batches`: وجبات العمل
- `batch_samples`: عينات كل وجبة
- `test_results`: نتائج التحاليل
- `sample_types`: أنواع العينات
- `sender_organizations`: جهات الإرسال
- `available_tests`: التحاليل المتاحة
- `technicians`: الفنيين

## استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في استيراد المكتبات**:
   - تأكد من تثبيت جميع المكتبات المطلوبة
   - استخدم `pip install -r requirements.txt`

2. **مشاكل في الطباعة**:
   - تأكد من تثبيت الطابعة وتشغيلها
   - تحقق من إعدادات الطابعة في تبويب الإعدادات

3. **مشاكل في قاعدة البيانات**:
   - سيتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل
   - تأكد من وجود صلاحيات الكتابة في مجلد البرنامج

4. **مشاكل في استيراد Excel**:
   - تأكد من تنسيق ملف Excel الصحيح
   - تحقق من وجود العناوين المطلوبة

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا البرنامج مطور خصيصاً لمختبر الصحة العامة المركزي - ذي قار.

---

**ملاحظة**: تأكد من عمل نسخة احتياطية من قاعدة البيانات بانتظام للحفاظ على البيانات.
