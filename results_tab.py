import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sqlite3

class ResultsTab:
    def __init__(self, parent, db, colors, font):
        self.parent = parent
        self.db = db
        self.colors = colors
        self.font = font
        self.current_batch_id = None
        self.result_options = ['Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND']
        self.selected_item_info = None  # لتخزين معلومات العنصر المختار

        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء عناصر واجهة تبويب النتائج"""
        # الإطار الرئيسي
        self.main_frame = tk.Frame(self.parent, bg=self.colors['white'])
        
        # عنوان التبويب
        title_label = tk.Label(
            self.main_frame,
            text="إدخال وعرض نتائج التحاليل",
            font=('Arial', 14, 'bold'),
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        title_label.pack(pady=10)
        
        # إطار اختيار الوجبة
        self.create_batch_selection_frame()
        
        # إطار النتائج
        self.create_results_frame()
        
        # تحديث البيانات
        self.refresh_data()
        
    def create_batch_selection_frame(self):
        """إنشاء إطار اختيار الوجبة"""
        selection_frame = tk.LabelFrame(
            self.main_frame,
            text="اختيار الوجبة",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        selection_frame.pack(fill='x', padx=20, pady=10)
        
        # إطار للحقول
        fields_frame = tk.Frame(selection_frame, bg=self.colors['white'])
        fields_frame.pack(fill='x', padx=10, pady=10)
        
        # رقم الوجبة
        tk.Label(fields_frame, text="رقم الوجبة:", font=self.font, bg=self.colors['white']).pack(side='left')
        
        self.batch_number_var = tk.StringVar()
        batch_entry = tk.Entry(fields_frame, textvariable=self.batch_number_var, font=self.font, width=15)
        batch_entry.pack(side='left', padx=5)
        
        # زر العرض
        show_btn = tk.Button(
            fields_frame,
            text="عرض",
            command=self.load_batch_results,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=10,
            height=1
        )
        show_btn.pack(side='left', padx=5)
        
        # قائمة الوجبات المكتملة
        tk.Label(fields_frame, text="أو اختر من الوجبات المكتملة:", font=self.font, bg=self.colors['white']).pack(side='left', padx=(20, 5))
        
        self.completed_batches_var = tk.StringVar()
        self.completed_batches_combo = ttk.Combobox(
            fields_frame,
            textvariable=self.completed_batches_var,
            font=self.font,
            width=20,
            state='readonly'
        )
        self.completed_batches_combo.pack(side='left', padx=5)
        self.completed_batches_combo.bind('<<ComboboxSelected>>', self.on_batch_combo_select)
        
    def create_results_frame(self):
        """إنشاء إطار النتائج"""
        results_frame = tk.LabelFrame(
            self.main_frame,
            text="نتائج التحاليل",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        results_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # معلومات الوجبة
        self.batch_info_label = tk.Label(
            results_frame,
            text="اختر وجبة لعرض النتائج",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        self.batch_info_label.pack(pady=5)
        
        # إطار النتائج الجماعية (منفصل ومميز)
        group_section = tk.LabelFrame(
            results_frame,
            text="🔄 تطبيق نتيجة على جميع العينات (جماعي)",
            font=('Arial', 10, 'bold'),
            bg=self.colors['white'],
            fg=self.colors['primary'],
            relief='groove',
            bd=2
        )
        group_section.pack(fill='x', padx=10, pady=5)

        # إطار المحتوى داخل القسم الجماعي
        group_content = tk.Frame(group_section, bg=self.colors['white'])
        group_content.pack(fill='x', padx=10, pady=8)

        # تعليمات للنتائج الجماعية
        group_instruction = tk.Label(
            group_content,
            text="⚠️ تحذير: سيتم تطبيق النتيجة على جميع التحاليل في الوجبة المعروضة",
            font=('Arial', 9),
            bg=self.colors['white'],
            fg='#DC3545'
        )
        group_instruction.pack(pady=(0, 8))

        # إطار الأزرار
        buttons_frame = tk.Frame(group_content, bg=self.colors['white'])
        buttons_frame.pack()

        # أزرار النتائج الجماعية مع رموز
        result_configs = [
            ('Negative', '#28A745', '🟢'),
            ('Positive', '#DC3545', '🔴'),
            ('Retest', '#FFC107', '🟡'),
            ('Recollection', '#17A2B8', '🔵'),
            ('Sent', '#6C757D', '📤'),
            ('TND', '#6F42C1', '🟣')
        ]

        for result, color, icon in result_configs:
            btn = tk.Button(
                buttons_frame,
                text=f"{icon} {result}",
                command=lambda r=result: self.apply_result_to_all(r),
                font=('Arial', 9, 'bold'),
                bg=color,
                fg=self.colors['white'],
                relief='raised',
                bd=3,
                width=12,
                height=2
            )
            btn.pack(side='left', padx=3, pady=2)
        
        # جدول النتائج
        columns = ('الرقم الوطني', 'اسم المريض', 'نوع العينة', 'التحليل', 'النتيجة', 'الفني', 'تاريخ النتيجة')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            if col == 'اسم المريض':
                self.results_tree.column(col, width=150, anchor='center')
            elif col == 'النتيجة':
                self.results_tree.column(col, width=100, anchor='center')
            else:
                self.results_tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        results_scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=results_scrollbar.set)
        
        # تخطيط الجدول
        self.results_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        results_scrollbar.pack(side='right', fill='y', pady=10)
        
        # إطار النتائج الفردية (منفصل ومميز)
        individual_section = tk.LabelFrame(
            results_frame,
            text="🎯 تطبيق نتيجة على عينة واحدة (فردي)",
            font=('Arial', 10, 'bold'),
            bg=self.colors['white'],
            fg=self.colors['secondary'],
            relief='groove',
            bd=2
        )
        individual_section.pack(fill='x', padx=10, pady=10)

        # إطار المحتوى داخل القسم الفردي
        individual_content = tk.Frame(individual_section, bg=self.colors['white'])
        individual_content.pack(fill='x', padx=10, pady=8)

        # تعليمات الاستخدام
        instruction_label = tk.Label(
            individual_content,
            text="📋 التعليمات: 1️⃣ اختر عينة من الجدول أعلاه ← 2️⃣ اختر النتيجة من القائمة ← 3️⃣ اضغط تطبيق",
            font=('Arial', 9),
            bg=self.colors['white'],
            fg=self.colors['text'],
            wraplength=600
        )
        instruction_label.pack(pady=(0, 8))

        # إطار عناصر التحكم
        controls_frame = tk.Frame(individual_content, bg=self.colors['white'])
        controls_frame.pack(fill='x')

        # تسمية القائمة المنسدلة
        result_label = tk.Label(
            controls_frame,
            text="🔽 اختر النتيجة:",
            font=('Arial', 10, 'bold'),
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        result_label.pack(side='left', padx=(0, 8))

        # القائمة المنسدلة للنتائج
        self.individual_result_var = tk.StringVar()
        individual_result_combo = ttk.Combobox(
            controls_frame,
            textvariable=self.individual_result_var,
            values=self.result_options,
            font=('Arial', 10),
            width=18,
            state='readonly'
        )
        individual_result_combo.pack(side='left', padx=5)

        # إضافة placeholder للقائمة المنسدلة
        individual_result_combo.set("-- اختر النتيجة --")

        # زر التطبيق الفردي
        apply_individual_btn = tk.Button(
            controls_frame,
            text="✅ تطبيق على العينة المختارة",
            command=self.apply_individual_result,
            font=('Arial', 10, 'bold'),
            bg='#17A2B8',
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=25,
            height=2
        )
        apply_individual_btn.pack(side='left', padx=10)

        # مؤشر حالة الاختيار
        self.selection_status_label = tk.Label(
            controls_frame,
            text="⚪ لم يتم اختيار عينة",
            font=('Arial', 9),
            bg=self.colors['white'],
            fg='gray'
        )
        self.selection_status_label.pack(side='right', padx=10)
        
        # زر حفظ النتائج
        save_btn = tk.Button(
            individual_result_frame,
            text="حفظ جميع النتائج",
            command=self.save_all_results,
            font=self.font,
            bg='#28A745',
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=15
        )
        save_btn.pack(side='right', padx=5)
        
        # ربط حدث النقر على الجدول
        self.results_tree.bind('<ButtonRelease-1>', self.on_result_select)
        
    def on_batch_combo_select(self, event):
        """معالج حدث اختيار وجبة من القائمة المنسدلة"""
        selected = self.completed_batches_var.get()
        if selected:
            batch_number = selected.split(' - ')[0].replace('الوجبة ', '')
            self.batch_number_var.set(batch_number)
            self.load_batch_results()
    
    def load_batch_results(self):
        """تحميل نتائج الوجبة"""
        batch_number = self.batch_number_var.get().strip()
        
        if not batch_number:
            messagebox.showwarning("تحذير", "يرجى إدخال رقم الوجبة")
            return
        
        try:
            batch_number = int(batch_number)
        except ValueError:
            messagebox.showerror("خطأ", "رقم الوجبة يجب أن يكون رقماً")
            return
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # البحث عن الوجبة
            cursor.execute('''
                SELECT id, batch_number, start_date, end_date, work_date, status
                FROM batches WHERE batch_number = ?
            ''', (batch_number,))
            
            batch_info = cursor.fetchone()
            
            if not batch_info:
                messagebox.showerror("خطأ", "لم يتم العثور على الوجبة")
                conn.close()
                return
            
            if batch_info[5] != 'completed':
                messagebox.showwarning("تحذير", "هذه الوجبة لم تكتمل بعد")
            
            self.current_batch_id = batch_info[0]
            
            # تحديث معلومات الوجبة
            info_text = f"الوجبة رقم {batch_info[1]} - من {batch_info[2]} إلى {batch_info[3]} - تاريخ العمل: {batch_info[4]}"
            self.batch_info_label.config(text=info_text)
            
            # تحميل النتائج
            self.load_results_data()
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل الوجبة: {str(e)}")
    
    def load_results_data(self):
        """تحميل بيانات النتائج"""
        # مسح البيانات الحالية
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        if not self.current_batch_id:
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # الحصول على جميع التحاليل للوجبة
            cursor.execute('''
                SELECT p.national_id, p.name, p.sample_type, pt.test_name,
                       tr.result, bs.technician_name, tr.created_at
                FROM batch_samples bs
                JOIN patients p ON bs.patient_id = p.id
                JOIN patient_tests pt ON p.id = pt.patient_id
                LEFT JOIN test_results tr ON (tr.patient_id = p.id AND tr.test_name = pt.test_name AND tr.batch_id = ?)
                WHERE bs.batch_id = ?
                ORDER BY p.national_id, pt.test_name
            ''', (self.current_batch_id, self.current_batch_id))

            for row in cursor.fetchall():
                result = row[4] if row[4] else "لم يتم إدخال النتيجة"
                technician = row[5] if row[5] else "غير محدد"
                result_date = row[6] if row[6] else ""

                # إدراج الصف
                item_id = self.results_tree.insert('', 'end', values=(
                    row[0], row[1], row[2], row[3], result, technician, result_date
                ))

                # تلوين الصف حسب النتيجة
                if result == "Positive":
                    self.results_tree.set(item_id, 'النتيجة', f"🔴 {result}")
                elif result == "Negative":
                    self.results_tree.set(item_id, 'النتيجة', f"🟢 {result}")
                elif result == "Retest":
                    self.results_tree.set(item_id, 'النتيجة', f"🟡 {result}")
                elif result == "Recollection":
                    self.results_tree.set(item_id, 'النتيجة', f"🔵 {result}")
                elif result == "Sent":
                    self.results_tree.set(item_id, 'النتيجة', f"📤 {result}")
                elif result == "TND":
                    self.results_tree.set(item_id, 'النتيجة', f"🟣 {result}")
                elif result == "لم يتم إدخال النتيجة":
                    self.results_tree.set(item_id, 'النتيجة', f"⚪ {result}")

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل النتائج: {str(e)}")
    
    def on_result_select(self, event):
        """معالج حدث اختيار نتيجة"""
        print("=== بداية on_result_select ===")

        selection = self.results_tree.selection()
        print(f"العناصر المختارة: {selection}")

        if selection:
            item = self.results_tree.item(selection[0])
            values = item['values']
            print(f"قيم العنصر: {values}")

            if len(values) >= 5:
                current_result = values[4]
                print(f"النتيجة الحالية (مع الرموز): '{current_result}'")

                # إزالة الرموز الملونة من النتيجة للحصول على النص الأصلي
                clean_result = current_result
                if current_result:
                    # إزالة جميع الرموز الملونة
                    symbols_to_remove = ["🔴 ", "🟢 ", "🟡 ", "🔵 ", "📤 ", "🟣 ", "⚪ "]
                    for symbol in symbols_to_remove:
                        clean_result = clean_result.replace(symbol, "")

                print(f"النتيجة النظيفة: '{clean_result}'")
                print(f"خيارات النتائج المتاحة: {self.result_options}")

                # تحديد النتيجة الحالية في القائمة المنسدلة
                if clean_result and clean_result != "لم يتم إدخال النتيجة":
                    # التحقق من أن النتيجة موجودة في القائمة
                    if clean_result in self.result_options:
                        print(f"تحديد النتيجة في القائمة: {clean_result}")
                        self.individual_result_var.set(clean_result)
                    else:
                        print(f"النتيجة '{clean_result}' غير موجودة في القائمة")
                        self.individual_result_var.set("")
                else:
                    print("لا توجد نتيجة أو النتيجة فارغة")
                    self.individual_result_var.set("")

                # عرض معلومات العنصر المختار
                if len(values) >= 4:
                    national_id = values[0]
                    patient_name = values[1]
                    test_name = values[3]

                    print(f"تم اختيار: {patient_name} - {test_name} - النتيجة: {clean_result}")

                    # تحديث متغير للتحقق من الاختيار
                    self.selected_item_info = {
                        'national_id': national_id,
                        'patient_name': patient_name,
                        'test_name': test_name,
                        'current_result': clean_result
                    }

                    # تحديث مؤشر حالة الاختيار
                    status_text = f"✅ تم اختيار: {patient_name} - {test_name}"
                    self.selection_status_label.config(
                        text=status_text,
                        fg='green'
                    )
                else:
                    print("بيانات العنصر غير مكتملة")
                    self.selection_status_label.config(
                        text="❌ بيانات العنصر غير مكتملة",
                        fg='red'
                    )
            else:
                print("عدد القيم غير كافي")
                self.selection_status_label.config(
                    text="❌ عدد القيم غير كافي",
                    fg='red'
                )
        else:
            print("لا يوجد عنصر مختار")
            self.individual_result_var.set("-- اختر النتيجة --")
            self.selection_status_label.config(
                text="⚪ لم يتم اختيار عينة",
                fg='gray'
            )

        print("=== انتهاء on_result_select ===")
        print(f"القيمة النهائية في القائمة المنسدلة: '{self.individual_result_var.get()}'")
        print()
    
    def apply_result_to_all(self, result):
        """تطبيق نتيجة على جميع العينات"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وجبة أولاً")
            return
        
        # حساب عدد التحاليل أولاً
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT COUNT(*)
                FROM batch_samples bs
                JOIN patients p ON bs.patient_id = p.id
                JOIN patient_tests pt ON p.id = pt.patient_id
                WHERE bs.batch_id = ?
            ''', (self.current_batch_id,))

            total_tests = cursor.fetchone()[0]
            conn.close()

            if total_tests == 0:
                messagebox.showwarning("تحذير", "لا توجد تحاليل في هذه الوجبة")
                return

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حساب التحاليل: {str(e)}")
            return

        # رسالة تأكيد مفصلة
        confirm_msg = f"هل تريد تطبيق النتيجة '{result}' على جميع التحاليل؟\n\n"
        confirm_msg += f"عدد التحاليل التي ستتأثر: {total_tests}\n"
        confirm_msg += "سيتم استبدال جميع النتائج الموجودة."

        if messagebox.askyesno("تأكيد التطبيق الجماعي", confirm_msg):
            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # الحصول على جميع التحاليل في الوجبة
                cursor.execute('''
                    SELECT p.id, pt.test_name
                    FROM batch_samples bs
                    JOIN patients p ON bs.patient_id = p.id
                    JOIN patient_tests pt ON p.id = pt.patient_id
                    WHERE bs.batch_id = ?
                ''', (self.current_batch_id,))

                tests = cursor.fetchall()
                updated_count = 0

                for patient_id, test_name in tests:
                    # حذف النتيجة السابقة إن وجدت
                    cursor.execute('''
                        DELETE FROM test_results
                        WHERE batch_id = ? AND patient_id = ? AND test_name = ?
                    ''', (self.current_batch_id, patient_id, test_name))

                    # إدراج النتيجة الجديدة
                    cursor.execute('''
                        INSERT INTO test_results (batch_id, patient_id, test_name, result)
                        VALUES (?, ?, ?, ?)
                    ''', (self.current_batch_id, patient_id, test_name, result))

                    updated_count += 1

                conn.commit()
                conn.close()

                # تحديث العرض
                self.load_results_data()
                messagebox.showinfo("نجح", f"تم تطبيق النتيجة '{result}' على {updated_count} تحليل بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء تطبيق النتيجة: {str(e)}")
    
    def apply_individual_result(self):
        """تطبيق نتيجة على التحليل المختار"""
        print("=== بداية apply_individual_result ===")

        # التحقق من اختيار العنصر
        selection = self.results_tree.selection()
        print(f"العناصر المختارة: {selection}")

        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل من الجدول أولاً")
            return

        # التحقق من اختيار النتيجة
        result = self.individual_result_var.get()
        print(f"النتيجة المختارة: '{result}'")

        if not result or result.strip() == "" or result == "-- اختر النتيجة --":
            messagebox.showwarning("تحذير", "يرجى اختيار نتيجة من القائمة المنسدلة")
            return

        # التحقق من وجود الوجبة
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وجبة أولاً من تبويب النتائج")
            return

        try:
            # استخراج بيانات العنصر المختار
            item = self.results_tree.item(selection[0])
            values = item['values']
            print(f"قيم العنصر المختار: {values}")

            if len(values) < 4:
                messagebox.showerror("خطأ", "بيانات العنصر المختار غير مكتملة")
                return

            national_id = values[0]
            patient_name = values[1]
            test_name = values[3]

            print(f"الرقم الوطني: {national_id}")
            print(f"اسم المريض: {patient_name}")
            print(f"اسم التحليل: {test_name}")

            # عرض رسالة تأكيد مع تفاصيل العملية
            confirm_msg = f"هل تريد تطبيق النتيجة '{result}' على:\n\n"
            confirm_msg += f"المريض: {patient_name}\n"
            confirm_msg += f"التحليل: {test_name}\n"
            confirm_msg += f"الرقم الوطني: {national_id}"

            if not messagebox.askyesno("تأكيد تطبيق النتيجة", confirm_msg):
                print("المستخدم ألغى العملية")
                return

            print("بدء الاتصال بقاعدة البيانات...")
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # الحصول على معرف المريض
            print(f"البحث عن المريض برقم وطني: {national_id}")
            cursor.execute('SELECT id FROM patients WHERE national_id = ?', (national_id,))
            patient_result = cursor.fetchone()
            print(f"نتيجة البحث عن المريض: {patient_result}")

            if not patient_result:
                messagebox.showerror("خطأ", f"لم يتم العثور على المريض برقم وطني: {national_id}")
                conn.close()
                return

            patient_id = patient_result[0]
            print(f"معرف المريض: {patient_id}")

            # التحقق من وجود التحليل للمريض
            print(f"التحقق من وجود التحليل '{test_name}' للمريض {patient_id}")
            cursor.execute('''
                SELECT COUNT(*) FROM patient_tests
                WHERE patient_id = ? AND test_name = ?
            ''', (patient_id, test_name))

            test_exists = cursor.fetchone()[0]
            print(f"عدد التحاليل الموجودة: {test_exists}")

            if test_exists == 0:
                messagebox.showerror("خطأ", f"التحليل '{test_name}' غير مسجل للمريض")
                conn.close()
                return

            # حذف النتيجة السابقة إن وجدت
            print(f"حذف النتائج السابقة للوجبة {self.current_batch_id}")
            cursor.execute('''
                DELETE FROM test_results
                WHERE batch_id = ? AND patient_id = ? AND test_name = ?
            ''', (self.current_batch_id, patient_id, test_name))
            deleted_rows = cursor.rowcount
            print(f"تم حذف {deleted_rows} نتيجة سابقة")

            # إدراج النتيجة الجديدة
            print(f"إدراج النتيجة الجديدة: {result}")
            cursor.execute('''
                INSERT INTO test_results (batch_id, patient_id, test_name, result)
                VALUES (?, ?, ?, ?)
            ''', (self.current_batch_id, patient_id, test_name, result))
            print("تم إدراج النتيجة الجديدة")

            conn.commit()
            conn.close()
            print("تم حفظ التغييرات في قاعدة البيانات")

            # تحديث العرض
            print("تحديث عرض النتائج...")
            self.load_results_data()

            success_msg = f"✅ تم تحديث النتيجة بنجاح!\n\n"
            success_msg += f"المريض: {patient_name}\n"
            success_msg += f"التحليل: {test_name}\n"
            success_msg += f"النتيجة الجديدة: {result}"

            messagebox.showinfo("نجح", success_msg)

            # إعادة تعيين القائمة المنسدلة ومؤشر الحالة
            self.individual_result_var.set("-- اختر النتيجة --")
            self.selection_status_label.config(
                text="✅ تم تطبيق النتيجة بنجاح - اختر عينة أخرى",
                fg='green'
            )
            self.selected_item_info = None

            print("=== انتهاء apply_individual_result بنجاح ===")

        except Exception as e:
            error_msg = f"حدث خطأ أثناء تحديث النتيجة:\n{str(e)}"
            messagebox.showerror("خطأ", error_msg)
            # طباعة تفاصيل الخطأ للتشخيص
            import traceback
            print(f"تفاصيل الخطأ: {traceback.format_exc()}")
            print("=== انتهاء apply_individual_result بخطأ ===")
    
    def save_all_results(self):
        """حفظ جميع النتائج"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وجبة أولاً")
            return
        
        messagebox.showinfo("نجح", "تم حفظ جميع النتائج بنجاح")
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.refresh_completed_batches()
    
    def refresh_completed_batches(self):
        """تحديث قائمة الوجبات المكتملة"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT batch_number, work_date, COUNT(bs.patient_id) as sample_count
                FROM batches b
                LEFT JOIN batch_samples bs ON b.id = bs.batch_id
                WHERE b.status = 'completed'
                GROUP BY b.id
                ORDER BY b.batch_number DESC
            ''')
            
            batches = []
            for row in cursor.fetchall():
                batch_text = f"الوجبة {row[0]} - {row[1]} ({row[2]} عينة)"
                batches.append(batch_text)
            
            self.completed_batches_combo['values'] = batches
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث قائمة الوجبات: {str(e)}")
    
    def show(self):
        """إظهار التبويب"""
        self.main_frame.pack(fill='both', expand=True)
    
    def hide(self):
        """إخفاء التبويب"""
        self.main_frame.pack_forget()
