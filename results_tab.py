import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sqlite3

class ResultsTab:
    def __init__(self, parent, db, colors, font):
        self.parent = parent
        self.db = db
        self.colors = colors
        self.font = font
        self.current_batch_id = None
        self.result_options = ['Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND']
        self.selected_item_info = None  # لتخزين معلومات العنصر المختار

        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء عناصر واجهة تبويب النتائج"""
        # الإطار الرئيسي
        self.main_frame = tk.Frame(self.parent, bg=self.colors['white'])
        
        # عنوان التبويب
        title_label = tk.Label(
            self.main_frame,
            text="إدخال وعرض نتائج التحاليل",
            font=('Arial', 14, 'bold'),
            bg=self.colors['white'],
            fg=self.colors['primary']
        )
        title_label.pack(pady=10)
        
        # إطار اختيار الوجبة
        self.create_batch_selection_frame()
        
        # إطار النتائج
        self.create_results_frame()
        
        # تحديث البيانات
        self.refresh_data()
        
    def create_batch_selection_frame(self):
        """إنشاء إطار اختيار الوجبة"""
        selection_frame = tk.LabelFrame(
            self.main_frame,
            text="اختيار الوجبة",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        selection_frame.pack(fill='x', padx=20, pady=10)
        
        # إطار للحقول
        fields_frame = tk.Frame(selection_frame, bg=self.colors['white'])
        fields_frame.pack(fill='x', padx=10, pady=10)
        
        # رقم الوجبة
        tk.Label(fields_frame, text="رقم الوجبة:", font=self.font, bg=self.colors['white']).pack(side='left')
        
        self.batch_number_var = tk.StringVar()
        batch_entry = tk.Entry(fields_frame, textvariable=self.batch_number_var, font=self.font, width=15)
        batch_entry.pack(side='left', padx=5)
        
        # زر العرض
        show_btn = tk.Button(
            fields_frame,
            text="عرض",
            command=self.load_batch_results,
            font=self.font,
            bg=self.colors['primary'],
            fg=self.colors['white'],
            relief='raised',
            bd=3,
            width=10,
            height=1
        )
        show_btn.pack(side='left', padx=5)
        
        # قائمة الوجبات المكتملة
        tk.Label(fields_frame, text="أو اختر من الوجبات المكتملة:", font=self.font, bg=self.colors['white']).pack(side='left', padx=(20, 5))
        
        self.completed_batches_var = tk.StringVar()
        self.completed_batches_combo = ttk.Combobox(
            fields_frame,
            textvariable=self.completed_batches_var,
            font=self.font,
            width=20,
            state='readonly'
        )
        self.completed_batches_combo.pack(side='left', padx=5)
        self.completed_batches_combo.bind('<<ComboboxSelected>>', self.on_batch_combo_select)
        
    def create_results_frame(self):
        """إنشاء إطار النتائج"""
        results_frame = tk.LabelFrame(
            self.main_frame,
            text="نتائج التحاليل",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        results_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # معلومات الوجبة
        self.batch_info_label = tk.Label(
            results_frame,
            text="اختر وجبة لعرض النتائج",
            font=self.font,
            bg=self.colors['white'],
            fg=self.colors['text']
        )
        self.batch_info_label.pack(pady=5)
        
        # إطار النتائج الفردية (أزرار مباشرة)
        results_section = tk.LabelFrame(
            results_frame,
            text="🎯 اختيار النتيجة للعينة المختارة",
            font=('Arial', 12, 'bold'),
            bg=self.colors['white'],
            fg=self.colors['primary'],
            relief='groove',
            bd=2
        )
        results_section.pack(fill='x', padx=10, pady=5)

        # إطار المحتوى داخل قسم النتائج
        results_content = tk.Frame(results_section, bg=self.colors['white'])
        results_content.pack(fill='x', padx=10, pady=8)

        # تعليمات الاستخدام
        instruction_label = tk.Label(
            results_content,
            text="📋 التعليمات: 1️⃣ اختر عينة من الجدول أعلاه ← 2️⃣ اضغط على النتيجة المطلوبة",
            font=('Arial', 10),
            bg=self.colors['white'],
            fg=self.colors['text'],
            wraplength=600
        )
        instruction_label.pack(pady=(0, 10))

        # إطار أزرار النتائج
        buttons_frame = tk.Frame(results_content, bg=self.colors['white'])
        buttons_frame.pack()

        # أزرار النتائج مع رموز وألوان مميزة
        result_configs = [
            ('Negative', '#28A745', '🟢'),
            ('Positive', '#DC3545', '🔴'),
            ('Retest', '#FFC107', '🟡'),
            ('Recollection', '#17A2B8', '🔵'),
            ('Sent', '#6C757D', '📤'),
            ('TND', '#6F42C1', '🟣')
        ]

        for result, color, icon in result_configs:
            btn = tk.Button(
                buttons_frame,
                text=f"{icon} {result}",
                command=lambda r=result: self.apply_individual_result_direct(r),
                font=('Arial', 10, 'bold'),
                bg=color,
                fg=self.colors['white'],
                relief='raised',
                bd=3,
                width=14,
                height=2
            )
            btn.pack(side='left', padx=5, pady=2)
        
        # جدول النتائج
        columns = ('الرقم الوطني', 'اسم المريض', 'نوع العينة', 'التحليل', 'النتيجة', 'الفني', 'تاريخ النتيجة')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            if col == 'اسم المريض':
                self.results_tree.column(col, width=150, anchor='center')
            elif col == 'النتيجة':
                self.results_tree.column(col, width=100, anchor='center')
            else:
                self.results_tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        results_scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=results_scrollbar.set)
        
        # تخطيط الجدول
        self.results_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        results_scrollbar.pack(side='right', fill='y', pady=10)
        
        # مؤشر حالة الاختيار
        status_frame = tk.Frame(results_content, bg=self.colors['white'])
        status_frame.pack(fill='x', pady=(10, 0))

        self.selection_status_label = tk.Label(
            status_frame,
            text="⚪ لم يتم اختيار عينة - اختر عينة من الجدول أعلاه",
            font=('Arial', 10, 'bold'),
            bg=self.colors['white'],
            fg='gray'
        )
        self.selection_status_label.pack()
        
        # ربط حدث النقر على الجدول
        self.results_tree.bind('<ButtonRelease-1>', self.on_result_select)
        
    def on_batch_combo_select(self, event):
        """معالج حدث اختيار وجبة من القائمة المنسدلة"""
        selected = self.completed_batches_var.get()
        if selected:
            batch_number = selected.split(' - ')[0].replace('الوجبة ', '')
            self.batch_number_var.set(batch_number)
            self.load_batch_results()
    
    def load_batch_results(self):
        """تحميل نتائج الوجبة"""
        batch_number = self.batch_number_var.get().strip()
        
        if not batch_number:
            messagebox.showwarning("تحذير", "يرجى إدخال رقم الوجبة")
            return
        
        try:
            batch_number = int(batch_number)
        except ValueError:
            messagebox.showerror("خطأ", "رقم الوجبة يجب أن يكون رقماً")
            return
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # البحث عن الوجبة
            cursor.execute('''
                SELECT id, batch_number, start_date, end_date, work_date, status
                FROM batches WHERE batch_number = ?
            ''', (batch_number,))
            
            batch_info = cursor.fetchone()
            
            if not batch_info:
                messagebox.showerror("خطأ", "لم يتم العثور على الوجبة")
                conn.close()
                return
            
            if batch_info[5] != 'completed':
                messagebox.showwarning("تحذير", "هذه الوجبة لم تكتمل بعد")
            
            self.current_batch_id = batch_info[0]
            
            # تحديث معلومات الوجبة
            info_text = f"الوجبة رقم {batch_info[1]} - من {batch_info[2]} إلى {batch_info[3]} - تاريخ العمل: {batch_info[4]}"
            self.batch_info_label.config(text=info_text)
            
            # تحميل النتائج
            self.load_results_data()
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل الوجبة: {str(e)}")
    
    def load_results_data(self):
        """تحميل بيانات النتائج"""
        # مسح البيانات الحالية
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        if not self.current_batch_id:
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # الحصول على جميع التحاليل للوجبة
            cursor.execute('''
                SELECT p.national_id, p.name, p.sample_type, pt.test_name,
                       tr.result, tr.technician, tr.result_date
                FROM batch_samples bs
                JOIN patients p ON bs.patient_id = p.id
                JOIN patient_tests pt ON p.id = pt.patient_id
                LEFT JOIN test_results tr ON (tr.patient_id = p.id AND tr.test_name = pt.test_name AND tr.batch_id = ?)
                WHERE bs.batch_id = ?
                ORDER BY p.national_id, pt.test_name
            ''', (self.current_batch_id, self.current_batch_id))

            for row in cursor.fetchall():
                result = row[4] if row[4] else "لم يتم إدخال النتيجة"
                technician = row[5] if row[5] else "غير محدد"
                result_date = row[6] if row[6] else ""

                # إدراج الصف
                item_id = self.results_tree.insert('', 'end', values=(
                    row[0], row[1], row[2], row[3], result, technician, result_date
                ))

                # تلوين الصف حسب النتيجة
                if result == "Positive":
                    self.results_tree.set(item_id, 'النتيجة', f"🔴 {result}")
                elif result == "Negative":
                    self.results_tree.set(item_id, 'النتيجة', f"🟢 {result}")
                elif result == "Retest":
                    self.results_tree.set(item_id, 'النتيجة', f"🟡 {result}")
                elif result == "Recollection":
                    self.results_tree.set(item_id, 'النتيجة', f"🔵 {result}")
                elif result == "Sent":
                    self.results_tree.set(item_id, 'النتيجة', f"📤 {result}")
                elif result == "TND":
                    self.results_tree.set(item_id, 'النتيجة', f"🟣 {result}")
                elif result == "لم يتم إدخال النتيجة":
                    self.results_tree.set(item_id, 'النتيجة', f"⚪ {result}")

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل النتائج: {str(e)}")
    
    def on_result_select(self, event):
        """معالج حدث اختيار عينة من الجدول"""
        selection = self.results_tree.selection()

        if selection:
            item = self.results_tree.item(selection[0])
            values = item['values']

            if len(values) >= 4:
                national_id = values[0]
                patient_name = values[1]
                test_name = values[3]
                current_result = values[4] if len(values) >= 5 else "لم يتم إدخال النتيجة"

                # إزالة الرموز الملونة من النتيجة
                clean_result = current_result
                if current_result:
                    symbols_to_remove = ["🔴 ", "🟢 ", "🟡 ", "🔵 ", "📤 ", "🟣 ", "⚪ "]
                    for symbol in symbols_to_remove:
                        clean_result = clean_result.replace(symbol, "")

                # حفظ معلومات العنصر المختار
                self.selected_item_info = {
                    'national_id': national_id,
                    'patient_name': patient_name,
                    'test_name': test_name,
                    'current_result': clean_result
                }

                # تحديث مؤشر حالة الاختيار
                status_text = f"✅ تم اختيار: {patient_name} - {test_name}"
                if clean_result != "لم يتم إدخال النتيجة":
                    status_text += f" (النتيجة الحالية: {clean_result})"

                self.selection_status_label.config(
                    text=status_text,
                    fg='green'
                )
            else:
                self.selection_status_label.config(
                    text="❌ بيانات العنصر غير مكتملة",
                    fg='red'
                )
        else:
            self.selected_item_info = None
            self.selection_status_label.config(
                text="⚪ لم يتم اختيار عينة - اختر عينة من الجدول أعلاه",
                fg='gray'
            )

    def apply_individual_result_direct(self, result):
        """تطبيق نتيجة مباشرة على العينة المختارة"""
        print(f"=== بداية apply_individual_result_direct مع النتيجة: {result} ===")

        # التحقق من اختيار العنصر
        if not self.selected_item_info:
            messagebox.showwarning("تحذير", "يرجى اختيار عينة من الجدول أولاً")
            return

        # التحقق من وجود الوجبة
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وجبة أولاً")
            return

        try:
            # استخراج معلومات العنصر المختار
            national_id = self.selected_item_info['national_id']
            patient_name = self.selected_item_info['patient_name']
            test_name = self.selected_item_info['test_name']

            # عرض رسالة تأكيد
            confirm_msg = f"هل تريد تطبيق النتيجة '{result}' على:\n\n"
            confirm_msg += f"المريض: {patient_name}\n"
            confirm_msg += f"التحليل: {test_name}\n"
            confirm_msg += f"الرقم الوطني: {national_id}"

            if not messagebox.askyesno("تأكيد تطبيق النتيجة", confirm_msg):
                print("المستخدم ألغى العملية")
                return

            # الاتصال بقاعدة البيانات
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # الحصول على معرف المريض
            cursor.execute('SELECT id FROM patients WHERE national_id = ?', (national_id,))
            patient_result = cursor.fetchone()

            if not patient_result:
                messagebox.showerror("خطأ", f"لم يتم العثور على المريض برقم وطني: {national_id}")
                conn.close()
                return

            patient_id = patient_result[0]

            # التحقق من وجود التحليل للمريض
            cursor.execute('''
                SELECT COUNT(*) FROM patient_tests
                WHERE patient_id = ? AND test_name = ?
            ''', (patient_id, test_name))

            test_exists = cursor.fetchone()[0]
            if test_exists == 0:
                messagebox.showerror("خطأ", f"التحليل '{test_name}' غير مسجل للمريض")
                conn.close()
                return

            # حذف النتيجة السابقة إن وجدت
            cursor.execute('''
                DELETE FROM test_results
                WHERE batch_id = ? AND patient_id = ? AND test_name = ?
            ''', (self.current_batch_id, patient_id, test_name))

            # إدراج النتيجة الجديدة مع تاريخ الحفظ
            import datetime
            save_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            technician_name = "الفني الحالي"

            cursor.execute('''
                INSERT INTO test_results (batch_id, patient_id, test_name, result, result_date, technician)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (self.current_batch_id, patient_id, test_name, result, save_date, technician_name))

            conn.commit()
            conn.close()

            # تحديث العرض
            self.load_results_data()

            # رسالة نجاح
            success_msg = f"✅ تم تطبيق وحفظ النتيجة بنجاح!\n\n"
            success_msg += f"المريض: {patient_name}\n"
            success_msg += f"التحليل: {test_name}\n"
            success_msg += f"النتيجة: {result}\n"
            success_msg += f"تاريخ الحفظ: {save_date}"

            messagebox.showinfo("نجح", success_msg)

            # تحديث مؤشر الحالة
            self.selection_status_label.config(
                text=f"✅ تم حفظ النتيجة '{result}' للمريض {patient_name}",
                fg='green'
            )

            print(f"=== انتهاء apply_individual_result_direct بنجاح ===")

        except Exception as e:
            error_msg = f"حدث خطأ أثناء تطبيق النتيجة:\n{str(e)}"
            messagebox.showerror("خطأ", error_msg)
            import traceback
            print(f"تفاصيل الخطأ: {traceback.format_exc()}")
            print("=== انتهاء apply_individual_result_direct بخطأ ===")

    def apply_result_to_all(self, result):
        """تطبيق نتيجة على جميع العينات"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وجبة أولاً")
            return
        
        # حساب عدد التحاليل أولاً
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT COUNT(*)
                FROM batch_samples bs
                JOIN patients p ON bs.patient_id = p.id
                JOIN patient_tests pt ON p.id = pt.patient_id
                WHERE bs.batch_id = ?
            ''', (self.current_batch_id,))

            total_tests = cursor.fetchone()[0]
            conn.close()

            if total_tests == 0:
                messagebox.showwarning("تحذير", "لا توجد تحاليل في هذه الوجبة")
                return

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حساب التحاليل: {str(e)}")
            return

        # رسالة تأكيد مفصلة
        confirm_msg = f"هل تريد تطبيق النتيجة '{result}' على جميع التحاليل؟\n\n"
        confirm_msg += f"عدد التحاليل التي ستتأثر: {total_tests}\n"
        confirm_msg += "سيتم استبدال جميع النتائج الموجودة."

        if messagebox.askyesno("تأكيد التطبيق الجماعي", confirm_msg):
            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # الحصول على جميع التحاليل في الوجبة
                cursor.execute('''
                    SELECT p.id, pt.test_name
                    FROM batch_samples bs
                    JOIN patients p ON bs.patient_id = p.id
                    JOIN patient_tests pt ON p.id = pt.patient_id
                    WHERE bs.batch_id = ?
                ''', (self.current_batch_id,))

                tests = cursor.fetchall()
                updated_count = 0

                for patient_id, test_name in tests:
                    # حذف النتيجة السابقة إن وجدت
                    cursor.execute('''
                        DELETE FROM test_results
                        WHERE batch_id = ? AND patient_id = ? AND test_name = ?
                    ''', (self.current_batch_id, patient_id, test_name))

                    # إدراج النتيجة الجديدة
                    cursor.execute('''
                        INSERT INTO test_results (batch_id, patient_id, test_name, result)
                        VALUES (?, ?, ?, ?)
                    ''', (self.current_batch_id, patient_id, test_name, result))

                    updated_count += 1

                conn.commit()
                conn.close()

                # تحديث العرض
                self.load_results_data()
                messagebox.showinfo("نجح", f"تم تطبيق النتيجة '{result}' على {updated_count} تحليل بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء تطبيق النتيجة: {str(e)}")
    
    def save_all_results(self):
        """حفظ جميع النتائج"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى اختيار وجبة أولاً")
            return

        messagebox.showinfo("نجح", "تم حفظ جميع النتائج بنجاح")
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.refresh_completed_batches()
    
    def refresh_completed_batches(self):
        """تحديث قائمة الوجبات المكتملة"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT batch_number, work_date, COUNT(bs.patient_id) as sample_count
                FROM batches b
                LEFT JOIN batch_samples bs ON b.id = bs.batch_id
                WHERE b.status = 'completed'
                GROUP BY b.id
                ORDER BY b.batch_number DESC
            ''')
            
            batches = []
            for row in cursor.fetchall():
                batch_text = f"الوجبة {row[0]} - {row[1]} ({row[2]} عينة)"
                batches.append(batch_text)
            
            self.completed_batches_combo['values'] = batches
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحديث قائمة الوجبات: {str(e)}")
    
    def show(self):
        """إظهار التبويب"""
        self.main_frame.pack(fill='both', expand=True)
    
    def hide(self):
        """إخفاء التبويب"""
        self.main_frame.pack_forget()
