#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف Excel تجريبي لاختبار استيراد البيانات
"""

import openpyxl
from datetime import datetime, timedelta
import random

def create_sample_excel():
    """إنشاء ملف Excel تجريبي"""
    
    # إنشاء مصنف جديد
    workbook = openpyxl.Workbook()
    sheet = workbook.active
    sheet.title = "بيانات المرضى"
    
    # العناوين
    headers = [
        'الاسم', 'العمر', 'الجنس', 'العنوان', 'رقم الهاتف',
        'نوع العينة', 'جهة الإرسال', 'تاريخ سحب العينة',
        'رقم الجواز', 'رقم الوصل', 'التحاليل'
    ]
    
    # إضافة العناوين
    for col, header in enumerate(headers, 1):
        sheet.cell(row=1, column=col, value=header)
        sheet.cell(row=1, column=col).font = openpyxl.styles.Font(bold=True)
    
    # بيانات تجريبية
    sample_names = [
        'أحمد محمد علي', 'فاطمة حسن أحمد', 'علي عبدالله محمد', 'زينب صالح حسين',
        'محمد عبدالرحمن علي', 'مريم أحمد حسن', 'حسن علي محمد', 'نور فاضل عبدالله',
        'عبدالله محمد حسين', 'سارة عادل أحمد', 'يوسف حامد علي', 'رقية محسن حسن',
        'عمر فاروق محمد', 'هدى عبدالكريم أحمد', 'كريم صباح علي', 'آمنة رشيد حسين'
    ]
    
    addresses = [
        'الناصرية - حي الجمهورية', 'الشطرة - حي الأطباء', 'سوق الشيوخ - المركز',
        'الناصرية - حي الحسين', 'الشطرة - حي الزهراء', 'الجبايش - المركز',
        'الناصرية - حي الأندلس', 'الشطرة - حي السلام', 'سوق الشيوخ - حي النور',
        'الناصرية - حي الكرامة', 'الشطرة - حي الوحدة', 'الجبايش - حي الشهداء'
    ]
    
    sample_types = ['دم', 'بول', 'براز', 'مسحة', 'بلغم']
    
    organizations = [
        'مستشفى الناصرية العام', 'مستشفى الحبوبي', 'مركز صحي الشطرة',
        'مستشفى سوق الشيوخ', 'مركز صحي الجبايش', 'مستشفى الشطرة العام'
    ]
    
    tests = [
        'فحص كوفيد-19', 'فحص الملاريا', 'فحص السل', 'فحص الكوليرا',
        'فحص التيفوئيد', 'فحص الدم الشامل', 'فحص البول'
    ]
    
    # إنشاء البيانات التجريبية
    for i in range(20):  # 20 مريض تجريبي
        row = i + 2
        
        # الاسم
        name = random.choice(sample_names)
        sheet.cell(row=row, column=1, value=name)
        
        # العمر
        age = random.randint(18, 80)
        sheet.cell(row=row, column=2, value=age)
        
        # الجنس
        gender = random.choice(['M', 'F'])
        sheet.cell(row=row, column=3, value=gender)
        
        # العنوان
        address = random.choice(addresses)
        sheet.cell(row=row, column=4, value=address)
        
        # رقم الهاتف
        phone = f"078{random.randint(10000000, 99999999)}"
        sheet.cell(row=row, column=5, value=phone)
        
        # نوع العينة
        sample_type = random.choice(sample_types)
        sheet.cell(row=row, column=6, value=sample_type)
        
        # جهة الإرسال
        org = random.choice(organizations)
        sheet.cell(row=row, column=7, value=org)
        
        # تاريخ سحب العينة
        days_ago = random.randint(0, 30)
        collection_date = datetime.now() - timedelta(days=days_ago)
        sheet.cell(row=row, column=8, value=collection_date.strftime("%Y-%m-%d"))
        
        # رقم الجواز (اختياري)
        if random.choice([True, False]):
            passport = f"A{random.randint(1000000, 9999999)}"
            sheet.cell(row=row, column=9, value=passport)
        
        # رقم الوصل (اختياري)
        if random.choice([True, False]):
            receipt = f"R{random.randint(100, 999)}"
            sheet.cell(row=row, column=10, value=receipt)
        
        # التحاليل
        selected_tests = random.sample(tests, random.randint(1, 3))
        tests_str = ', '.join(selected_tests)
        sheet.cell(row=row, column=11, value=tests_str)
    
    # تنسيق الأعمدة
    for col in range(1, len(headers) + 1):
        column_letter = openpyxl.utils.get_column_letter(col)
        if col in [1, 4, 6, 7, 11]:  # أعمدة النص الطويل
            sheet.column_dimensions[column_letter].width = 25
        elif col in [8]:  # عمود التاريخ
            sheet.column_dimensions[column_letter].width = 15
        else:
            sheet.column_dimensions[column_letter].width = 12
    
    # حفظ الملف
    filename = "بيانات_تجريبية_للاستيراد.xlsx"
    workbook.save(filename)
    
    print(f"تم إنشاء ملف Excel التجريبي: {filename}")
    print(f"يحتوي على 20 مريض تجريبي")
    
    return filename

if __name__ == "__main__":
    create_sample_excel()
