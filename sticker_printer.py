import tkinter as tk
from tkinter import messagebox
import win32print
import win32ui
from PIL import Image, ImageDraw, ImageFont
import tempfile
import os

class StickerPrinter:
    def __init__(self):
        self.printer_name = self.get_default_printer()
        
    def get_default_printer(self):
        """الحصول على الطابعة الافتراضية"""
        try:
            return win32print.GetDefaultPrinter()
        except:
            # إذا لم تكن هناك طابعة افتراضية، البحث عن أي طابعة متاحة
            printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]
            if printers:
                return printers[0]
            return None
    
    def get_available_printers(self):
        """الحصول على قائمة الطابعات المتاحة"""
        try:
            printers = []
            # طابعات محلية
            local_printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL)]
            printers.extend(local_printers)
            
            # طابعات الشبكة
            try:
                network_printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_CONNECTIONS)]
                printers.extend(network_printers)
            except:
                pass
            
            return printers
        except Exception as e:
            print(f"خطأ في الحصول على الطابعات: {e}")
            return []
    
    def set_printer(self, printer_name):
        """تحديد الطابعة المستخدمة"""
        self.printer_name = printer_name
    
    def create_sticker_image(self, patient_name, sample_type, national_id):
        """إنشاء صورة الستيكر"""
        # أبعاد الستيكر (بالبكسل)
        width, height = 400, 200
        
        # إنشاء صورة جديدة
        image = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(image)
        
        try:
            # محاولة استخدام خط عربي
            font_large = ImageFont.truetype("arial.ttf", 16)
            font_medium = ImageFont.truetype("arial.ttf", 14)
            font_small = ImageFont.truetype("arial.ttf", 12)
        except:
            # استخدام الخط الافتراضي إذا لم يتم العثور على الخط
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # رسم إطار
        draw.rectangle([5, 5, width-5, height-5], outline='black', width=2)
        
        # عنوان المختبر
        lab_title = "مختبر الصحة العامة المركزي - ذي قار"
        draw.text((10, 15), lab_title, fill='black', font=font_medium)
        
        # خط فاصل
        draw.line([10, 40, width-10, 40], fill='black', width=1)
        
        # بيانات المريض
        y_pos = 50
        
        # اسم المريض
        draw.text((10, y_pos), f"الاسم: {patient_name}", fill='black', font=font_large)
        y_pos += 25
        
        # نوع العينة
        draw.text((10, y_pos), f"نوع العينة: {sample_type}", fill='black', font=font_medium)
        y_pos += 20
        
        # الرقم الوطني
        draw.text((10, y_pos), f"الرقم الوطني: {national_id}", fill='black', font=font_medium)
        y_pos += 20
        
        # التاريخ
        from datetime import datetime
        current_date = datetime.now().strftime("%Y-%m-%d %H:%M")
        draw.text((10, y_pos), f"التاريخ: {current_date}", fill='black', font=font_small)
        
        # باركود بسيط (خطوط عمودية تمثل الرقم الوطني)
        barcode_x = width - 80
        barcode_y = 60
        barcode_width = 60
        barcode_height = 40
        
        # رسم خطوط الباركود
        for i, digit in enumerate(str(national_id)):
            x = barcode_x + (i * 6)
            line_height = barcode_height - (int(digit) * 3)
            draw.rectangle([x, barcode_y, x+3, barcode_y + line_height], fill='black')
        
        return image
    
    def print_sticker(self, patient_name, sample_type, national_id):
        """طباعة الستيكر"""
        if not self.printer_name:
            messagebox.showerror("خطأ", "لم يتم العثور على طابعة متاحة")
            return False
        
        try:
            # إنشاء صورة الستيكر
            sticker_image = self.create_sticker_image(patient_name, sample_type, national_id)
            
            # حفظ الصورة مؤقتاً
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.bmp')
            sticker_image.save(temp_file.name, 'BMP')
            temp_file.close()
            
            # طباعة الصورة
            self.print_image_file(temp_file.name)
            
            # حذف الملف المؤقت
            os.unlink(temp_file.name)
            
            return True
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء طباعة الستيكر: {str(e)}")
            return False
    
    def print_image_file(self, image_path):
        """طباعة ملف صورة"""
        try:
            # فتح الطابعة
            hprinter = win32print.OpenPrinter(self.printer_name)
            
            try:
                # بدء مهمة الطباعة
                job_info = ("Sticker Print Job", None, None)
                job_id = win32print.StartDocPrinter(hprinter, 1, job_info)
                
                try:
                    win32print.StartPagePrinter(hprinter)
                    
                    # إنشاء سياق الجهاز للطباعة
                    hdc = win32ui.CreateDC()
                    hdc.CreatePrinterDC(self.printer_name)
                    
                    # تحميل الصورة وطباعتها
                    bmp = Image.open(image_path)
                    
                    # تحويل الصورة إلى تنسيق مناسب للطباعة
                    dib = ImageWin.Dib(bmp)
                    
                    # طباعة الصورة
                    dib.draw(hdc.GetHandleOutput(), (0, 0, bmp.size[0], bmp.size[1]))
                    
                    hdc.DeleteDC()
                    win32print.EndPagePrinter(hprinter)
                    
                finally:
                    win32print.EndDocPrinter(hprinter)
                    
            finally:
                win32print.ClosePrinter(hprinter)
                
        except Exception as e:
            # طريقة بديلة للطباعة باستخدام الأوامر
            self.print_using_system_command(image_path)
    
    def print_using_system_command(self, image_path):
        """طباعة باستخدام أوامر النظام كطريقة بديلة"""
        try:
            import subprocess
            
            # استخدام أمر النظام لطباعة الصورة
            if self.printer_name:
                cmd = f'mspaint /pt "{image_path}" "{self.printer_name}"'
            else:
                cmd = f'mspaint /p "{image_path}"'
            
            subprocess.run(cmd, shell=True, check=True)
            
        except Exception as e:
            raise Exception(f"فشل في طباعة الستيكر: {str(e)}")
    
    def test_printer(self):
        """اختبار الطابعة"""
        try:
            if not self.printer_name:
                return False, "لم يتم العثور على طابعة"
            
            # إنشاء ستيكر تجريبي
            test_image = self.create_sticker_image("اختبار الطباعة", "عينة تجريبية", "TEST001")
            
            # حفظ مؤقت
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.bmp')
            test_image.save(temp_file.name, 'BMP')
            temp_file.close()
            
            # محاولة الطباعة
            self.print_image_file(temp_file.name)
            
            # حذف الملف المؤقت
            os.unlink(temp_file.name)
            
            return True, "تم اختبار الطابعة بنجاح"
            
        except Exception as e:
            return False, f"فشل اختبار الطابعة: {str(e)}"

# استيراد ImageWin إذا كان متاحاً
try:
    from PIL import ImageWin
except ImportError:
    # إذا لم يكن متاحاً، سنستخدم الطريقة البديلة فقط
    ImageWin = None
